from enum import Enum
from typing import Optional
from app.models import UserProject, Role, Permission, RolePermission, Project
from app.helpers.extensions import db


class ProjectRole(Enum):
    """Project-specific roles"""
    OWNER = "project_owner"
    MEMBER = "project_member"
    VIEWER = "project_viewer"


class ProjectPermission(Enum):
    """Project-specific permissions"""
    # Project management
    VIEW_PROJECT = "view_project"
    EDIT_PROJECT = "edit_project"
    DELETE_PROJECT = "delete_project"
    MANAGE_MEMBERS = "manage_project_members"
    
    # Task management
    CREATE_TASK = "create_task"
    EDIT_TASK = "edit_task"
    DELETE_TASK = "delete_task"
    ASSIGN_TASK = "assign_task"
    
    # Comments and attachments
    ADD_COMMENT = "add_comment"
    EDIT_COMMENT = "edit_comment"
    DELETE_COMMENT = "delete_comment"
    UPLOAD_ATTACHMENT = "upload_attachment"
    DELETE_ATTACHMENT = "delete_attachment"


class ProjectPermission<PERSON>hecker:
    """Helper class to check project permissions"""
    
    @staticmethod
    def has_project_permission(user_id: int, project_id: int, permission: ProjectPermission) -> bool:
        """Check if user has specific permission in project"""
        try:
            # Check if user is project creator (always has all permissions)
            project = Project.query.get(project_id)
            if not project:
                return False
            
            if project.created_by == user_id:
                return True
            
            # Check through UserProject relationship
            user_project = UserProject.query.filter_by(
                user_id=user_id,
                project_id=project_id
            ).first()
            
            if not user_project:
                return False
            
            # Get role permissions
            role_permissions = RolePermission.query.filter_by(
                role_id=user_project.role_id
            ).all()
            
            permission_names = []
            for rp in role_permissions:
                perm = Permission.query.get(rp.permission_id)
                if perm:
                    permission_names.append(perm.name)
            
            return permission.value in permission_names
            
        except Exception:
            return False
    
    @staticmethod
    def has_project_role(user_id: int, project_id: int, role: ProjectRole) -> bool:
        """Check if user has specific role in project"""
        try:
            # Check if user is project creator (always owner)
            project = Project.query.get(project_id)
            if not project:
                return False
            
            if project.created_by == user_id and role == ProjectRole.OWNER:
                return True
            
            # Check through UserProject relationship
            user_project = UserProject.query.filter_by(
                user_id=user_id,
                project_id=project_id
            ).first()
            
            if not user_project:
                return False
            
            role_obj = Role.query.get(user_project.role_id)
            return role_obj and role_obj.name == role.value
            
        except Exception:
            return False
    
    @staticmethod
    def get_user_project_role(user_id: int, project_id: int) -> Optional[str]:
        """Get user's role in project"""
        try:
            # Check if user is project creator
            project = Project.query.get(project_id)
            if not project:
                return None
            
            if project.created_by == user_id:
                return ProjectRole.OWNER.value
            
            # Check through UserProject relationship
            user_project = UserProject.query.filter_by(
                user_id=user_id,
                project_id=project_id
            ).first()
            
            if not user_project:
                return None
            
            role_obj = Role.query.get(user_project.role_id)
            return role_obj.name if role_obj else None
            
        except Exception:
            return None
    
    @staticmethod
    def can_access_project(user_id: int, project_id: int) -> bool:
        """Check if user can access project (has any role)"""
        return ProjectPermissionChecker.has_project_permission(
            user_id, project_id, ProjectPermission.VIEW_PROJECT
        )


def init_project_roles_and_permissions():
    """Initialize default project roles and permissions"""
    try:
        # Create project roles if they don't exist
        for role_enum in ProjectRole:
            existing_role = Role.query.filter_by(name=role_enum.value).first()
            if not existing_role:
                role = Role(
                    name=role_enum.value,
                    description=f"Project {role_enum.name.lower()} role",
                    created_by="system"
                )
                db.session.add(role)
        
        # Create project permissions if they don't exist
        for perm_enum in ProjectPermission:
            existing_perm = Permission.query.filter_by(name=perm_enum.value).first()
            if not existing_perm:
                permission = Permission(
                    name=perm_enum.value,
                    description=f"Permission to {perm_enum.value.replace('_', ' ')}"
                )
                db.session.add(permission)
        
        db.session.commit()
        
        # Set up role-permission mappings
        _setup_role_permissions()
        
    except Exception as e:
        db.session.rollback()
        print(f"Error initializing project roles and permissions: {e}")


def _setup_role_permissions():
    """Set up default role-permission mappings"""
    try:
        # Owner permissions (all permissions)
        owner_role = Role.query.filter_by(name=ProjectRole.OWNER.value).first()
        if owner_role:
            for perm_enum in ProjectPermission:
                permission = Permission.query.filter_by(name=perm_enum.value).first()
                if permission:
                    existing = RolePermission.query.filter_by(
                        role_id=owner_role.id,
                        permission_id=permission.id
                    ).first()
                    if not existing:
                        role_perm = RolePermission(
                            role_id=owner_role.id,
                            permission_id=permission.id
                        )
                        db.session.add(role_perm)
        
        # Member permissions (limited permissions)
        member_role = Role.query.filter_by(name=ProjectRole.MEMBER.value).first()
        member_permissions = [
            ProjectPermission.VIEW_PROJECT,
            ProjectPermission.CREATE_TASK,
            ProjectPermission.EDIT_TASK,
            ProjectPermission.ADD_COMMENT,
            ProjectPermission.EDIT_COMMENT,
            ProjectPermission.UPLOAD_ATTACHMENT
        ]
        
        if member_role:
            for perm_enum in member_permissions:
                permission = Permission.query.filter_by(name=perm_enum.value).first()
                if permission:
                    existing = RolePermission.query.filter_by(
                        role_id=member_role.id,
                        permission_id=permission.id
                    ).first()
                    if not existing:
                        role_perm = RolePermission(
                            role_id=member_role.id,
                            permission_id=permission.id
                        )
                        db.session.add(role_perm)
        
        # Viewer permissions (read-only)
        viewer_role = Role.query.filter_by(name=ProjectRole.VIEWER.value).first()
        viewer_permissions = [
            ProjectPermission.VIEW_PROJECT
        ]
        
        if viewer_role:
            for perm_enum in viewer_permissions:
                permission = Permission.query.filter_by(name=perm_enum.value).first()
                if permission:
                    existing = RolePermission.query.filter_by(
                        role_id=viewer_role.id,
                        permission_id=permission.id
                    ).first()
                    if not existing:
                        role_perm = RolePermission(
                            role_id=viewer_role.id,
                            permission_id=permission.id
                        )
                        db.session.add(role_perm)
        
        db.session.commit()
        
    except Exception as e:
        db.session.rollback()
        print(f"Error setting up role permissions: {e}")
