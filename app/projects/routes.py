from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.models.api_response import ApiResponse
from app.tasks.github_service import GitHubIssuesService
from app.projects.github_service import GitHubRepositoryService
from app.models.project import Project

from .services import (
    get_all_projects,
    get_project_by_id,
    create_project,
    update_project,
    delete_project,
    restore_project,
)

projects_bp = Blueprint("projects_bp", __name__, url_prefix="/projects")


@projects_bp.route("/", methods=["GET"])
@jwt_required()
def list_projects():
    """Get all projects for the current user"""
    current_user_id = int(get_jwt_identity())
    include_deleted = request.args.get("include_deleted", "false").lower() == "true"

    result, status = get_all_projects(current_user_id, include_deleted)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["GET"])
@jwt_required()
def get_project(project_id):
    """Get a specific project by ID"""
    current_user_id = int(get_jwt_identity())
    result, status = get_project_by_id(project_id, current_user_id)
    return jsonify(result), status


@projects_bp.route("/", methods=["POST"])
@jwt_required()
def create_project_endpoint():
    """Create a new project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        response = ApiResponse.failure("No data provided", code=400)
        return jsonify(response.to_dict()), response.code

    result, status = create_project(data, current_user_id)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["PUT"])
@jwt_required()
def update_project_endpoint(project_id):
    """Update an existing project"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        response = ApiResponse.failure("No data provided", code=400)
        return jsonify(response.to_dict()), response.code

    result, status = update_project(project_id, data, current_user_id)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>", methods=["DELETE"])
@jwt_required()
def delete_project_endpoint(project_id):
    """Delete a project (soft delete by default)"""
    current_user_id = int(get_jwt_identity())
    hard_delete = request.args.get("hard_delete", "false").lower() == "true"

    result, status = delete_project(project_id, current_user_id, hard_delete)
    return jsonify(result), status


@projects_bp.route("/<int:project_id>/restore", methods=["POST"])
@jwt_required()
def restore_project_endpoint(project_id):
    """Restore a soft-deleted project"""
    current_user_id = int(get_jwt_identity())
    result, status = restore_project(project_id, current_user_id)
    return jsonify(result), status


@projects_bp.route("/github/repositories", methods=["GET"])
@jwt_required()
def fetch_github_repositories():
    """Fetch all GitHub repositories for the authenticated user"""
    try:
        current_user_id = int(get_jwt_identity())
        github_service = GitHubRepositoryService(current_user_id)

        response = github_service.get_all_user_repositories()
        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(
            f"Error fetching repositories: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@projects_bp.route("/github/sync", methods=["POST"])
@jwt_required()
def sync_github_repositories():
    """Sync selected GitHub repositories to local projects"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        if not data or not data.get("repository_urls"):
            response = ApiResponse.failure("repository_urls is required", code=400)
            return jsonify(response.to_dict()), response.code

        repository_urls = data.get("repository_urls", [])
        if not isinstance(repository_urls, list):
            response = ApiResponse.failure("repository_urls must be a list", code=400)
            return jsonify(response.to_dict()), response.code

        github_service = GitHubRepositoryService(current_user_id)
        response = github_service.sync_repositories_to_projects(repository_urls)

        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(
            f"Error syncing repositories: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@projects_bp.route("/<int:project_id>/github/sync-complete", methods=["POST"])
@jwt_required()
def sync_project_complete(project_id):  # Add project_id parameter here
    """Complete sync: Update project info and sync all issues"""
    try:
        current_user_id = int(get_jwt_identity())

        project = Project.query.filter_by(
            id=project_id,
            user_id=current_user_id,
            deleted_at=None,
            is_github_synced=True,
        ).first()

        if not project:
            response = ApiResponse.failure(
                "Project not found or not synced from GitHub", code=404
            )
            return jsonify(response.to_dict()), response.code

        github_repo_service = GitHubRepositoryService(current_user_id)

        # Update project info first
        if project.github_repo_url:
            project_sync_response = github_repo_service.sync_repositories_to_projects(
                [project.github_repo_url]
            )
            if not project_sync_response.success:
                return (
                    jsonify(project_sync_response.to_dict()),
                    project_sync_response.code,
                )

        github_issues_service = GitHubIssuesService(current_user_id)
        issues_sync_response = github_issues_service.sync_repository_issues_to_tasks(
            project_id
        )

        if issues_sync_response.success:
            # Combine results
            combined_data = {
                "project_updated": True,
                "synced_tasks": issues_sync_response.data.get("synced_tasks", []),
                "total_synced": issues_sync_response.data.get("total_synced", 0),
                "total_issues": issues_sync_response.data.get("total_issues", 0),
                "errors": issues_sync_response.data.get("errors", []),
                "project": issues_sync_response.data.get("project", {}),
            }

            response = ApiResponse.success(
                "Project synced completely", data=combined_data
            )
        else:
            response = issues_sync_response

        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing project: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code
