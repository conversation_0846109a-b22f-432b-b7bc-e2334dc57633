"""
GitHub repository service for project synchronization
"""

from datetime import datetime
from typing import List
import requests

from app.models.user import User
from app.models.project import Project
from app.models.integration import Integration
from app.helpers.extensions import db
from app.models.api_response import ApiResponse


class GitHubRepositoryService:
    """Service for GitHub repository operations"""

    GITHUB_API_BASE_URL = "https://api.github.com"

    def __init__(self, user_id: int):
        """
        Initialize GitHub repository service for a specific user

        Args:
            user_id (int): User ID
        """
        self.user_id = user_id
        self.user = User.query.get(user_id)
        if not self.user:
            raise ValueError(f"User with ID {user_id} not found")

        # Get GitHub integration
        self.github_integration = Integration.query.filter_by(
            user_id=user_id, platform="github", is_active=True
        ).first()

        if not self.github_integration:
            raise ValueError("No active GitHub integration found for user")

        self.headers = {
            "Authorization": f"Bearer {self.github_integration.access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "TMS-Python-Backend",
        }

    def fetch_user_repositories(
        self, per_page: int = 100, page: int = 1
    ) -> ApiResponse:
        """
        Get all repositories for the authenticated user

        Args:
            per_page (int): Number of repositories per page (max 100)
            page (int): Page number

        Returns:
            ApiResponse: Response containing repositories data or error
        """
        try:
            url = f"{self.GITHUB_API_BASE_URL}/user/repos"
            params = {
                "per_page": min(per_page, 100),
                "page": page,
                "sort": "updated",
                "direction": "desc",
                "type": "all",  # all, owner, public, private, member
            }

            response = requests.get(
                url, headers=self.headers, params=params, timeout=30
            )

            if response.status_code != 200:
                return ApiResponse.failure(
                    f"Failed to fetch repositories: {response.status_code}",
                    code=response.status_code,
                )

            repositories = response.json()

            # Transform repository data to include only needed fields
            repo_data = []
            for repo in repositories:
                repo_info = {
                    "id": str(repo.get("id")),
                    "name": repo.get("name"),
                    "full_name": repo.get("full_name"),
                    "description": repo.get("description"),
                    "html_url": repo.get("html_url"),
                    "clone_url": repo.get("clone_url"),
                    "ssh_url": repo.get("ssh_url"),
                    "private": repo.get("private", False),
                    "fork": repo.get("fork", False),
                    "language": repo.get("language"),
                    "stargazers_count": repo.get("stargazers_count", 0),
                    "forks_count": repo.get("forks_count", 0),
                    "open_issues_count": repo.get("open_issues_count", 0),
                    "created_at": repo.get("created_at"),
                    "updated_at": repo.get("updated_at"),
                    "pushed_at": repo.get("pushed_at"),
                    "default_branch": repo.get("default_branch", "main"),
                    "owner": {
                        "login": repo.get("owner", {}).get("login"),
                        "id": repo.get("owner", {}).get("id"),
                        "avatar_url": repo.get("owner", {}).get("avatar_url"),
                    },
                }
                repo_data.append(repo_info)

            return ApiResponse.success(
                "Repositories fetched successfully",
                data={
                    "repositories": repo_data,
                    "total_count": len(repo_data),
                    "page": page,
                    "per_page": per_page,
                },
            )

        except requests.RequestException as e:
            return ApiResponse.failure(
                f"Network error while fetching repositories: {str(e)}", code=500
            )
        except Exception as e:
            return ApiResponse.failure(
                f"Unexpected error while fetching repositories: {str(e)}", code=500
            )

    def get_all_user_repositories(self) -> ApiResponse:
        """
        Fetch all repositories for the authenticated user from GitHub

        Returns:
            ApiResponse: Response containing all repositories data or error
        """
        try:
            all_repositories = []
            page = 1
            per_page = 100

            while True:
                response = self.fetch_user_repositories(per_page=per_page, page=page)

                if not response.success:
                    return response

                repositories = response.data.get("repositories", [])
                if not repositories:
                    break

                all_repositories.extend(repositories)

                # If we got less than per_page, we've reached the end
                if len(repositories) < per_page:
                    break

                page += 1

            return ApiResponse.success(
                "All repositories fetched successfully",
                data={
                    "repositories": all_repositories,
                    "total_count": len(all_repositories),
                },
            )

        except Exception as e:
            return ApiResponse.failure(
                f"Error fetching repositories: {str(e)}", code=500
            )

    def get_repository_info(self, owner: str, repo: str) -> ApiResponse:
        """
        Get detailed information about a specific repository

        Args:
            owner (str): Repository owner
            repo (str): Repository name

        Returns:
            ApiResponse: Response containing repository data or error
        """
        try:
            url = f"{self.GITHUB_API_BASE_URL}/repos/{owner}/{repo}"

            response = requests.get(url, headers=self.headers, timeout=30)

            if response.status_code != 200:
                return ApiResponse.failure(
                    f"Failed to fetch repository info: {response.status_code}",
                    code=response.status_code,
                )

            repo_data = response.json()

            repo_info = {
                "id": str(repo_data.get("id")),
                "name": repo_data.get("name"),
                "full_name": repo_data.get("full_name"),
                "description": repo_data.get("description"),
                "html_url": repo_data.get("html_url"),
                "clone_url": repo_data.get("clone_url"),
                "ssh_url": repo_data.get("ssh_url"),
                "private": repo_data.get("private", False),
                "fork": repo_data.get("fork", False),
                "language": repo_data.get("language"),
                "stargazers_count": repo_data.get("stargazers_count", 0),
                "forks_count": repo_data.get("forks_count", 0),
                "open_issues_count": repo_data.get("open_issues_count", 0),
                "created_at": repo_data.get("created_at"),
                "updated_at": repo_data.get("updated_at"),
                "pushed_at": repo_data.get("pushed_at"),
                "default_branch": repo_data.get("default_branch", "main"),
                "topics": repo_data.get("topics", []),
                "owner": {
                    "login": repo_data.get("owner", {}).get("login"),
                    "id": repo_data.get("owner", {}).get("id"),
                    "avatar_url": repo_data.get("owner", {}).get("avatar_url"),
                },
            }

            return ApiResponse.success(
                "Repository info fetched successfully", data=repo_info
            )

        except requests.RequestException as e:
            return ApiResponse.failure(
                f"Network error while fetching repository info: {str(e)}", code=500
            )
        except Exception as e:
            return ApiResponse.failure(
                f"Unexpected error while fetching repository info: {str(e)}", code=500
            )

    def sync_repositories_to_projects(self, repository_urls: List[str]) -> ApiResponse:
        """
        Sync selected GitHub repositories to local projects

        Args:
            repository_urls (List[str]): List of GitHub repository URLs to sync

        Returns:
            ApiResponse: Response containing sync results
        """
        try:
            synced_projects = []
            errors = []

            for repo_url in repository_urls:
                try:
                    # Extract owner and repo name from URL
                    # Expected format: https://github.com/owner/repo
                    if not repo_url.startswith("https://github.com/"):
                        errors.append(f"Invalid GitHub URL format: {repo_url}")
                        continue

                    url_parts = repo_url.replace("https://github.com/", "").split("/")
                    if len(url_parts) < 2:
                        errors.append(f"Invalid GitHub URL format: {repo_url}")
                        continue

                    owner = url_parts[0]
                    repo_name = url_parts[1]
                    full_name = f"{owner}/{repo_name}"

                    # Get repository info from GitHub
                    repo_response = self.get_repository_info(owner, repo_name)
                    if not repo_response.success:
                        errors.append(
                            f"Failed to fetch repository info for {full_name}: ",
                            f"{repo_response.message}",
                        )
                        continue

                    repo_data = repo_response.data

                    # Check if project already exists
                    existing_project = Project.query.filter_by(
                        github_repo_id=repo_data["id"],
                        user_id=self.user_id,
                        deleted_at=None,
                    ).first()

                    if existing_project:
                        # Update existing project
                        existing_project.name = repo_data["name"]
                        existing_project.description = repo_data["description"] or ""
                        existing_project.github_repo_name = repo_data["name"]
                        existing_project.github_repo_url = repo_data["html_url"]
                        existing_project.github_repo_full_name = repo_data["full_name"]
                        existing_project.last_github_sync = datetime.utcnow()
                        existing_project.updated_at = datetime.utcnow()

                        synced_projects.append(
                            {"action": "updated", "project": existing_project.to_dict()}
                        )
                    else:
                        # Create new project
                        new_project = Project(
                            name=repo_data["name"],
                            description=repo_data["description"] or "",
                            status="Active",
                            priority="Medium",
                            created_by=self.user_id,
                            user_id=self.user_id,
                            github_repo_id=repo_data["id"],
                            github_repo_name=repo_data["name"],
                            github_repo_url=repo_data["html_url"],
                            github_repo_full_name=repo_data["full_name"],
                            is_github_synced=True,
                            last_github_sync=datetime.utcnow(),
                        )

                        db.session.add(new_project)
                        db.session.flush()  # Get the ID

                        synced_projects.append(
                            {"action": "created", "project": new_project.to_dict()}
                        )

                except Exception as e:
                    errors.append(f"Error syncing repository {repo_url}: {str(e)}")

            db.session.commit()

            return ApiResponse.success(
                "Repositories synced successfully",
                data={
                    "synced_projects": synced_projects,
                    "total_synced": len(synced_projects),
                    "errors": errors,
                },
            )

        except Exception as e:
            db.session.rollback()
            return ApiResponse.failure(
                f"Error syncing repositories: {str(e)}", code=500
            )
