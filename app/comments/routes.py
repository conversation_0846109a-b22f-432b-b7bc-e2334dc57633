from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_task_comments, add_comment, update_comment, delete_comment

comments_bp = Blueprint("comments_bp", __name__)

@comments_bp.route("/comments/task/<int:task_id>", methods=["GET"])
@jwt_required()
def list_task_comments(task_id):
    """
    Retrieve all comments for a task
    ---
    tags:
      - Comments
    security:
      - BearerAuth: []
    parameters:
      - name: task_id
        in: path
        type: integer
        required: true
        description: ID of the task to retrieve comments for
    responses:
      200:
        description: List of comments
        schema:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              user_id:
                type: integer
              task_id:
                type: integer
              content:
                type: string
              created_at:
                type: string
    """
    return jsonify(get_task_comments(task_id))


@comments_bp.route("/comments/", methods=["POST"])
@jwt_required()
def add_comment_view():
    """
    Add a new comment to a task
    ---
    tags:
      - Comments
    security:
      - BearerAuth: []
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          required:
            - task_id
            - content
          properties:
            task_id:
              type: integer
              example: 1
            content:
              type: string
              example: This task needs clarification.
    responses:
      201:
        description: Comment successfully added
        schema:
          type: object
          properties:
            message:
              type: string
            id:
              type: integer
      404:
        description: Task not found
    """
    current_user_id = get_jwt_identity()
    data = request.get_json()
    return add_comment(data, current_user_id)


@comments_bp.route("/comments/<int:comment_id>", methods=["PUT"])
@jwt_required()
def update_comment_view(comment_id):
    """
    Update an existing comment
    ---
    tags:
      - Comments
    security:
      - BearerAuth: []
    parameters:
      - name: comment_id
        in: path
        type: integer
        required: true
        description: ID of the comment to update
      - in: body
        name: body
        required: true
        schema:
          type: object
          required:
            - content
          properties:
            content:
              type: string
              example: Updated comment content
    responses:
      200:
        description: Comment updated successfully
        schema:
          type: object
          properties:
            message:
              type: string
      403:
        description: Permission denied
      404:
        description: Comment not found
    """
    current_user_id = get_jwt_identity()
    data = request.get_json()
    return update_comment(comment_id, data, current_user_id)


@comments_bp.route("/comments/<int:comment_id>", methods=["DELETE"])
@jwt_required()
def delete_comment_view(comment_id):
    """
    Delete a comment
    ---
    tags:
      - Comments
    security:
      - BearerAuth: []
    parameters:
      - name: comment_id
        in: path
        type: integer
        required: true
        description: ID of the comment to delete
    responses:
      200:
        description: Comment deleted successfully
        schema:
          type: object
          properties:
            message:
              type: string
      403:
        description: Permission denied
      404:
        description: Comment not found
    """
    current_user_id = get_jwt_identity()
    return delete_comment(comment_id, current_user_id)
