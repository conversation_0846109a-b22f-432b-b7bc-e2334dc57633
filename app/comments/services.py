from app.models import Comment, Task
from app.helpers.extensions import db
from datetime import datetime

def get_task_comments(task_id):
    """
    Get all comments for a specific task
    """
    comments = Comment.query.filter_by(
        task_id=task_id,
        deleted_at=None
    ).order_by(Comment.created_at.desc()).all()

    return [{
        "id": c.id,
        "task_id": c.task_id,
        "user_id": c.user_id,
        "content": c.content,
        "created_at": c.created_at.isoformat()
    } for c in comments]


def add_comment(data, user_id):
    """
    Add a new comment to a task
    """
    task = Task.query.get_or_404(data["task_id"])

    comment = Comment(
        task_id=data["task_id"],
        user_id=int(user_id),
        content=data["content"]
    )
    db.session.add(comment)
    db.session.commit()
    return {"message": "Comment added", "id": comment.id}, 201


def update_comment(comment_id, data, user_id):
    """
    Update an existing comment
    """
    comment = Comment.query.get_or_404(comment_id)
    if int(comment.user_id) != int(user_id):
        return {"message": "Permission denied"}, 403

    comment.content = data["content"]
    db.session.commit()
    return {"message": "Comment updated"}, 200


def delete_comment(comment_id, user_id):
    """
    Soft delete a comment
    """
    comment = Comment.query.get_or_404(comment_id)
    if int(comment.user_id) != int(user_id):
        return {"message": "Permission denied"}, 403

    comment.deleted_at = datetime.utcnow()
    db.session.commit()
    return {"message": "Comment deleted"}, 200
