from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_marshmallow import Marshmallow
from flask_jwt_extended import <PERSON><PERSON><PERSON>anager
from datetime import datetime, timedelta

from app.helpers.jwt_blocklist import jwt_blocklist

db = SQLAlchemy()
migrate = Migrate()
ma = Marshmallow()
jwt = JWTManager()

def init_extensions(app):
    db.init_app(app)
    migrate.init_app(app, db)
    ma.init_app(app)
    jwt.init_app(app)

@jwt.token_in_blocklist_loader
def check_if_token_revoked(_unused, jwt_payload):
    jti = jwt_payload["jti"]
    return jti in jwt_blocklist

@jwt.additional_claims_loader
def add_claims_to_jwt(identity):
    """Add additional claims to JWT token"""
    try:
        from app.models.user import User, UserRole
        from app.models.auth import Role

        user_id = int(identity)

        # Get user roles
        roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()

        role_names = [role.name for role in roles]

        return {
            "roles": role_names,
            "is_admin": "admin" in role_names
        }
    except Exception:
        return {
            "roles": [],
            "is_admin": False
        }