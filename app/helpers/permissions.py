# Permission decorators and utilities
#
# Note: These permission decorators are currently not used in the application
# but are kept for potential future use. The actual permission checking
# logic is implemented in app/users/services.py to avoid circular imports
# and maintain better separation of concerns.
#
# Current permission checking is done manually in route handlers using
# the get_user_roles() function from app/users/services.py
#
# If you need to use these decorators in the future:
# from app.helpers.permissions import permission_required, role_required, admin_required

# Uncomment the code below if you need to use these decorators:

# from functools import wraps
# from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
# from flask import jsonify
# from app.models import User, RolePermission, Permission, Role, UserRole
# from app.helpers.extensions import db
# from app.models.api_response import ApiResponse

# def has_permission(user_id, permission_name):
#     """Check if the user has the given permission through their roles."""
#     # Implementation here...

# def has_role(user_id, role_name):
#     """Check if the user has the given role."""
#     # Implementation here...

# def permission_required(permission_name):
#     """Decorator to protect routes with a specific permission."""
#     # Implementation here...

# def role_required(role_name):
#     """Decorator to protect routes with a specific role."""
#     # Implementation here...

# def admin_required(fn):
#     """Decorator to protect routes for admin users only."""
#     # Implementation here...
