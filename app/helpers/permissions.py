from functools import wraps
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from flask import jsonify
from app.models import User, RolePermission, Permission, Role, UserRole
from app.helpers.extensions import db
from app.models.api_response import ApiResponse

def has_permission(user_id, permission_name):
    """
    Check if the user has the given permission through their roles.
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return False

        # Get all permissions through user roles
        permissions = db.session.query(Permission).join(RolePermission).join(Role).join(UserRole).filter(
            UserRole.user_id == user_id,
            Permission.name == permission_name
        ).first()

        return permissions is not None
    except Exception:
        return False

def has_role(user_id, role_name):
    """
    Check if the user has the given role.
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return False

        role = Role.query.filter_by(name=role_name).first()
        if not role:
            return False

        user_role = UserRole.query.filter_by(
            user_id=user_id,
            role_id=role.id
        ).first()

        return user_role is not None
    except Exception:
        return False

def is_admin(user_id):
    """
    Check if user has admin role.
    """
    return has_role(user_id, 'admin')

def permission_required(permission_name):
    """
    Decorator to protect routes with a specific permission.
    """
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            verify_jwt_in_request()
            user_id = int(get_jwt_identity())

            if not has_permission(user_id, permission_name):
                response = ApiResponse.failure("Permission denied", code=403)
                return jsonify(response.to_dict()), response.code

            return fn(*args, **kwargs)
        return wrapper
    return decorator

def role_required(role_name):
    """
    Decorator to protect routes with a specific role.
    """
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            verify_jwt_in_request()
            user_id = int(get_jwt_identity())

            if not has_role(user_id, role_name):
                response = ApiResponse.failure("Access denied. Required role not found", code=403)
                return jsonify(response.to_dict()), response.code

            return fn(*args, **kwargs)
        return wrapper
    return decorator

def admin_required(fn):
    """
    Decorator to protect routes for admin users only.
    """
    @wraps(fn)
    def wrapper(*args, **kwargs):
        verify_jwt_in_request()
        user_id = int(get_jwt_identity())

        if not is_admin(user_id):
            response = ApiResponse.failure("Admin access required", code=403)
            return jsonify(response.to_dict()), response.code

        return fn(*args, **kwargs)
    return wrapper

def get_user_roles(user_id):
    """
    Get all roles for a user.
    """
    try:
        roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()
        return [role.name for role in roles]
    except Exception:
        return []

def get_user_permissions(user_id):
    """
    Get all permissions for a user based on their roles.
    """
    try:
        permissions = db.session.query(Permission).join(RolePermission).join(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).distinct().all()
        return [permission.name for permission in permissions]
    except Exception:
        return []
