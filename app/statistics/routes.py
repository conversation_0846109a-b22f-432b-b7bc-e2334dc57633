from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import  get_project_statistics, get_user_statistics, get_dashboard_statistics, get_member_reliability
from app.models.api_response import ApiResponse


statistics_bp = Blueprint("statistics_bp", __name__, url_prefix="/statistics")


@statistics_bp.route("/dashboard", methods=["GET"])
@jwt_required()
def get_dashboard():
    """Get dashboard statistics for current user"""
    current_user_id = int(get_jwt_identity())
    result, status = get_dashboard_statistics(current_user_id)
    return jsonify(result), status

@statistics_bp.route("/user", methods=["GET"])
@jwt_required()
def get_user_stats():
    """Get detailed statistics for current user"""
    current_user_id = int(get_jwt_identity())
    result, status = get_user_statistics(current_user_id)
    return jsonify(result), status

@statistics_bp.route("/user/<int:user_id>", methods=["GET"])
@jwt_required()
def get_specific_user_stats(user_id):
    """Get statistics for a specific user (admin feature)"""
    # Note: In a real application, you might want to add admin permission check here
    result, status = get_user_statistics(user_id)
    return jsonify(result), status

@statistics_bp.route("/project/<int:project_id>", methods=["GET"])
@jwt_required()
def get_project_stats(project_id):
    """Get comprehensive statistics for a specific project"""
    current_user_id = int(get_jwt_identity())
    result, status = get_project_statistics(project_id, current_user_id)
    return jsonify(result), status

@statistics_bp.route("/reliability", methods=["GET"])
@jwt_required()
def get_current_user_reliability():
    """Get reliability metrics for current user"""
    current_user_id = int(get_jwt_identity())
    time_period = request.args.get('period', default=30, type=int)
    reliability = get_member_reliability(current_user_id, time_period)
    
    response = ApiResponse.success(
        "Reliability metrics retrieved successfully", 
        data={"user_id": current_user_id, "reliability": reliability}
    )
    return jsonify(response.to_dict()), 200


@statistics_bp.route("/reliability/<int:user_id>", methods=["GET"])
@jwt_required()
def get_user_reliability(user_id):
    """Get reliability metrics for a specific user"""
    time_period = request.args.get('period', default=30, type=int)
    reliability = get_member_reliability(user_id, time_period)
    
    response = ApiResponse.success(
        "Reliability metrics retrieved successfully", 
        data={"user_id": user_id, "reliability": reliability}
    )
    return jsonify(response.to_dict()), 200
