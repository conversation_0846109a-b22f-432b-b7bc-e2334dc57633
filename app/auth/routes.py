import secrets
from flask import (
    request,
    jsonify,
    Blueprint,
    redirect,
    session,
)
from flask_jwt_extended import (
    create_access_token,
    get_jwt,
    jwt_required,
    get_jwt_identity,
)
from werkzeug.security import check_password_hash
from app.auth.services import (
    create_user,
    get_user_by_email,
    get_user_by_id,
    get_user_with_roles,
    create_user_token,
    jwt_blocklist,
    forgot_password,
    reset_password,
    change_password,
    update_password,
    reset_session,
)
from app.auth.github_oauth import GitHubOAuthService
from app.auth.github_services import (
    create_or_update_github_user,
    link_github_account,
    unlink_github_account,
)
from app.models.api_response import ApiResponse

auth_bp = Blueprint("auth", __name__, url_prefix="/auth")


# GET METHODS
@auth_bp.route("/me", methods=["GET"])
@jwt_required()
def get_current_user():
    user_id = get_jwt_identity()
    user_data = get_user_with_roles(int(user_id))

    if not user_data:
        response = ApiResponse.failure("User not found", code=404)
        return jsonify(response.to_dict()), response.code

    response = ApiResponse.success(data=user_data)
    return jsonify(response.to_dict()), response.code


# POST METHODS
@auth_bp.route("/register", methods=["POST"])
def register():
    data = request.get_json()

    # Validate required fields according to openapi.yaml
    if not data:
        response = ApiResponse.failure("Request body is required", code=400)
        return jsonify(response.to_dict()), response.code

    required_fields = ["username", "email", "password"]
    for field in required_fields:
        if not data.get(field):
            response = ApiResponse.failure(
                f"{field.capitalize()} is required", code=400
            )
            return jsonify(response.to_dict()), response.code

    response = create_user(data)
    return jsonify(response.to_dict()), response.code


@auth_bp.route("/login", methods=["POST"])
def login():
    try:
        print("🔍 Login endpoint called")
        data = request.get_json()
        print(f"📥 Request data: {data}")

        # Validate required fields
        if not data:
            print("❌ No request body")
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        if not data.get("email") or not data.get("password"):
            print("❌ Missing email or password")
            response = ApiResponse.failure("Email and password are required", code=400)
            return jsonify(response.to_dict()), response.code

        print(f"🔍 Looking for user with email: {data.get('email')}")
        user = get_user_by_email(data.get("email"))

        if user:
            print(f"✅ User found: {user.username}")

            # Check if user is a GitHub OAuth user without password
            if not user.password_hash and user.auth_provider == "github":
                print("❌ GitHub OAuth user attempting password login")
                response = ApiResponse.failure(
                    "This account uses GitHub authentication. Please use GitHub login.",
                    code=400,
                )
                return jsonify(response.to_dict()), response.code

            # Check password for local users
            if user.password_hash and check_password_hash(
                user.password_hash, data.get("password")
            ):
                print("✅ Password correct")
                access_token = create_user_token(user.id)
                user_data = get_user_with_roles(user.id)

                # Return format matching LoginResponse schema
                response = ApiResponse.success(
                    "Login successful",
                    data={
                        "access_token": access_token,
                        "user": user_data
                    }
                )
                print(f"📤 Success response: {response.to_dict()}")
                return jsonify(response.to_dict()), response.code
            else:
                print("❌ Password incorrect")
        else:
            print("❌ User not found")

        # Return format matching ErrorResponse schema
        response = ApiResponse.failure("Invalid credentials", code=401)
        print(f"📤 Error response: {response.to_dict()}")
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        print(f"💥 Login error: {str(e)}")
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/logout", methods=["POST"])
@jwt_required()
def logout():
    jti = get_jwt()["jti"]
    exp = get_jwt()["exp"]
    jwt_blocklist[jti] = exp
    # Return format matching SuccessResponse schema
    response = ApiResponse.success("Logged out successfully")
    return jsonify(response.to_dict()), response.code


@auth_bp.route("/forgot-password", methods=["POST"])
def forgot_password_route():
    """Request password reset"""
    try:
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        email = data.get("email")
        if not email:
            response = ApiResponse.failure("Email is required", code=400)
            return jsonify(response.to_dict()), response.code

        response = forgot_password(email)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/reset-password", methods=["POST"])
def reset_password_route():
    """Reset password using token"""
    try:
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        token = data.get("token")
        new_password = data.get("new_password")

        if not token or not new_password:
            response = ApiResponse.failure("Token and new password are required", code=400)
            return jsonify(response.to_dict()), response.code

        response = reset_password(token, new_password)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/change-password", methods=["POST"])
@jwt_required()
def change_password_route():
    """Change password for authenticated user"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        current_password = data.get("current_password")
        new_password = data.get("new_password")

        if not current_password or not new_password:
            response = ApiResponse.failure(
                "Current password and new password are required",
                code=400
            )
            return jsonify(response.to_dict()), response.code

        response = change_password(int(user_id), current_password, new_password)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/update-password", methods=["POST"])
@jwt_required()
def update_password_route():
    """Update password for authenticated user (alias for change-password)"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data:
            response = ApiResponse.failure("Request body is required", code=400)
            return jsonify(response.to_dict()), response.code

        current_password = data.get("current_password")
        new_password = data.get("new_password")

        if not current_password or not new_password:
            response = ApiResponse.failure(
                "Current password and new password are required",
                code=400
            )
            return jsonify(response.to_dict()), response.code

        response = update_password(int(user_id), current_password, new_password)
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@auth_bp.route("/reset-session", methods=["POST"])
@jwt_required()
def reset_session_route():
    """Reset all sessions for the authenticated user"""
    try:
        user_id = get_jwt_identity()

        response = reset_session(int(user_id))
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

# GITHUB OAUTH METHODS
@auth_bp.route("/github/url", methods=["GET"])
def github_auth_url():
    """Get GitHub OAuth authorization URL (for Swagger UI testing)"""
    try:
        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)
        session["github_oauth_state"] = state

        # Get authorization URL
        auth_url = GitHubOAuthService.get_authorization_url(state=state)

        response = ApiResponse.success(
            "GitHub authorization URL generated",
            data={
                "authorization_url": auth_url,
                "instructions": [
                    "Copy the authorization_url above",
                    "Open it in a new browser tab",
                    "Authorize the application on GitHub",
                    "You will be redirected back with a JWT token",
                ],
            },
        )
        return jsonify(response.to_dict()), response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error generating GitHub OAuth URL: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github", methods=["GET"])
def github_login():
    """Initiate GitHub OAuth login"""
    try:
        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)
        session["github_oauth_state"] = state

        # Get authorization URL
        auth_url = GitHubOAuthService.get_authorization_url(state=state)

        return redirect(auth_url)

    except Exception as e:
        response = ApiResponse.failure(
            f"Error initiating GitHub OAuth: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github/callback", methods=["GET"])
def github_callback():
    """Handle GitHub OAuth callback"""
    try:
        # Get parameters from callback
        code = request.args.get("code")
        state = request.args.get("state")
        error = request.args.get("error")

        if error:
            response = ApiResponse.failure(f"GitHub OAuth error: {error}", code=400)
            return jsonify(response.to_dict()), response.code

        if not code:
            response = ApiResponse.failure("Authorization code not provided", code=400)
            return jsonify(response.to_dict()), response.code

        # Verify state parameter for CSRF protection
        stored_state = session.get("github_oauth_state")
        if not stored_state or stored_state != state:
            response = ApiResponse.failure("Invalid state parameter", code=400)
            return jsonify(response.to_dict()), response.code

        # Clear state from session
        session.pop("github_oauth_state", None)

        # Exchange code for token
        token_response = GitHubOAuthService.exchange_code_for_token(code, state)
        if not token_response.success:
            return jsonify(token_response.to_dict()), token_response.code

        access_token = token_response.data.get("access_token")
        if not access_token:
            response = ApiResponse.failure(
                "No access token received from GitHub", code=400
            )
            return jsonify(response.to_dict()), response.code

        # Get user information from GitHub
        user_info_response = GitHubOAuthService.get_user_info(access_token)
        if not user_info_response.success:
            return jsonify(user_info_response.to_dict()), user_info_response.code

        # Create or update user
        auth_response = create_or_update_github_user(
            user_info_response.data, access_token
        )

        return jsonify(auth_response.to_dict()), auth_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error processing GitHub callback: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github/link", methods=["POST"])
@jwt_required()
def link_github():
    """Link GitHub account to current user"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        if not data or not data.get("access_token"):
            response = ApiResponse.failure("GitHub access token is required", code=400)
            return jsonify(response.to_dict()), response.code

        access_token = data.get("access_token")

        # Get user information from GitHub
        user_info_response = GitHubOAuthService.get_user_info(access_token)
        if not user_info_response.success:
            return jsonify(user_info_response.to_dict()), user_info_response.code

        # Link GitHub account
        link_response = link_github_account(
            int(user_id), user_info_response.data, access_token
        )

        return jsonify(link_response.to_dict()), link_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error linking GitHub account: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code


@auth_bp.route("/github/unlink", methods=["POST"])
@jwt_required()
def unlink_github():
    """Unlink GitHub account from current user"""
    try:
        user_id = get_jwt_identity()

        # Unlink GitHub account
        unlink_response = unlink_github_account(int(user_id))

        return jsonify(unlink_response.to_dict()), unlink_response.code

    except Exception as e:
        response = ApiResponse.failure(
            f"Error unlinking GitHub account: {str(e)}", code=500
        )
        return jsonify(response.to_dict()), response.code
