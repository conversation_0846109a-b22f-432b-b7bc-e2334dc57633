"""
GitHub OAuth service for handling GitHub authentication
"""

from urllib.parse import urlencode

import requests
from flask import current_app
from app.models.api_response import ApiResponse


class GitHubOAuthService:
    """Service for handling GitHub OAuth authentication"""

    GITHUB_AUTHORIZE_URL = "https://github.com/login/oauth/authorize"
    GITHUB_TOKEN_URL = "https://github.com/login/oauth/access_token"
    GITHUB_USER_API_URL = "https://api.github.com/user"
    GITHUB_USER_EMAILS_API_URL = "https://api.github.com/user/emails"

    @staticmethod
    def get_authorization_url(state=None):
        """
        Generate GitHub OAuth authorization URL

        Args:
            state (str, optional): State parameter for CSRF protection

        Returns:
            str: Authorization URL
        """
        params = {
            "client_id": current_app.config["GITHUB_CLIENT_ID"],
            "redirect_uri": current_app.config["GITHUB_REDIRECT_URI"],
            "scope": "user:email",
            "response_type": "code",
        }

        if state:
            params["state"] = state

        return f"{GitHubOAuthService.GITHUB_AUTHORIZE_URL}?{urlencode(params)}"

    @staticmethod
    def exchange_code_for_token(code, state=None):
        """
        Exchange authorization code for access token

        Args:
            code (str): Authorization code from GitHub
            state (str, optional): State parameter for verification

        Returns:
            ApiResponse: Response containing token data or error
        """
        try:
            data = {
                "client_id": current_app.config["GITHUB_CLIENT_ID"],
                "client_secret": current_app.config["GITHUB_CLIENT_SECRET"],
                "code": code,
                "redirect_uri": current_app.config["GITHUB_REDIRECT_URI"],
            }

            headers = {"Accept": "application/json", "User-Agent": "TMS-Python-Backend"}

            response = requests.post(
                GitHubOAuthService.GITHUB_TOKEN_URL,
                data=data,
                headers=headers,
                timeout=10,
            )

            if response.status_code != 200:
                return ApiResponse.failure(
                    "Failed to exchange code for token", code=400
                )

            token_data = response.json()

            if "error" in token_data:
                return ApiResponse.failure(
                    f"GitHub OAuth error: {token_data.get('error_description', 'Unknown error')}",
                    code=400,
                )

            return ApiResponse.success("Token exchange successful", data=token_data)

        except requests.RequestException as e:
            return ApiResponse.failure(
                f"Network error during token exchange: {str(e)}", code=500
            )
        except Exception as e:
            return ApiResponse.failure(
                f"Unexpected error during token exchange: {str(e)}", code=500
            )

    @staticmethod
    def get_user_info(access_token):
        """
        Get user information from GitHub API

        Args:
            access_token (str): GitHub access token

        Returns:
            ApiResponse: Response containing user data or error
        """
        try:
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/vnd.github.v3+json",
                "User-Agent": "TMS-Python-Backend",
            }

            # Get user profile
            user_response = requests.get(
                GitHubOAuthService.GITHUB_USER_API_URL, headers=headers, timeout=10
            )

            if user_response.status_code != 200:
                return ApiResponse.failure(
                    "Failed to fetch user information from GitHub", code=400
                )

            user_data = user_response.json()

            # Get user emails
            emails_response = requests.get(
                GitHubOAuthService.GITHUB_USER_EMAILS_API_URL,
                headers=headers,
                timeout=10,
            )

            emails_data = []
            if emails_response.status_code == 200:
                emails_data = emails_response.json()

            # Find primary email
            primary_email = None
            for email_info in emails_data:
                if email_info.get("primary", False):
                    primary_email = email_info.get("email")
                    break

            # Fallback to public email if no primary email found
            if not primary_email:
                primary_email = user_data.get("email")

            # Prepare user info
            user_info = {
                "github_id": str(user_data.get("id")),
                "username": user_data.get("login"),
                "email": primary_email,
                "name": user_data.get("name"),
                "avatar_url": user_data.get("avatar_url"),
                "bio": user_data.get("bio"),
                "public_repos": user_data.get("public_repos"),
                "followers": user_data.get("followers"),
                "following": user_data.get("following"),
                "github_url": user_data.get("html_url"),
            }

            return ApiResponse.success(
                "User information retrieved successfully", data=user_info
            )

        except requests.RequestException as e:
            return ApiResponse.failure(
                f"Network error while fetching user info: {str(e)}", code=500
            )
        except Exception as e:
            return ApiResponse.failure(
                f"Unexpected error while fetching user info: {str(e)}", code=500
            )
