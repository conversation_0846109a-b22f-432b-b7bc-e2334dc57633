from werkzeug.security import generate_password_hash, check_password_hash
from flask_jwt_extended import create_access_token

from app.models.api_response import ApiResponse
from app.helpers.jwt_blocklist import jwt_blocklist
from app.helpers.validators import is_valid_email, is_strong_password
from app.models.user import User, PasswordResetToken, UserRole
from app.models.auth import Role
from app.helpers.extensions import db
from app.helpers.email_service import EmailService


def create_user(data):
    username = data.get("username", "").strip()
    email = data.get("email", "").strip()
    password = data.get("password", "")

    if not username or not email or not password:
        return ApiResponse.failure(
            "Username, email and password are required.", code=400
        )

    if username == "" or email == "":
        return ApiResponse.failure(
            "Username and email must not be empty or spaces only.", code=400
        )

    if not is_valid_email(email):
        return ApiResponse.failure("Invalid email format.", code=400)

    if not is_strong_password(password):
        return ApiResponse.failure(
            "Password must be at least 8 characters and include "
            "uppercase, lowercase, number, and special character.",
            code=400,
        )

    if User.query.filter_by(username=username).first():
        return ApiResponse.failure("Username already exists.", code=400)

    existing_user = User.query.filter_by(email=email).first()
    if existing_user:
        return ApiResponse.failure("Email already exists.", code=400)

    try:
        new_user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password),
            auth_provider='local'
        )
        db.session.add(new_user)
        db.session.flush()  # Get the user ID

        # Assign default 'member' role to new users
        member_role = Role.query.filter_by(name='member').first()
        if member_role:
            user_role = UserRole(user_id=new_user.id, role_id=member_role.id)
            db.session.add(user_role)

        db.session.commit()
        return ApiResponse.success("User created successfully", code=201)

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def get_user_by_email(email):
    return User.query.filter_by(email=email).first()


def get_user_by_id(user_id):
    return User.query.get(user_id)


def get_user_roles(user_id):
    """Get all roles for a user"""
    try:
        roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()
        return [role.name for role in roles]
    except Exception:
        return []


def create_user_token(user_id):
    """Create JWT token with user roles (claims added automatically via JWT extension)"""
    return create_access_token(identity=str(user_id))


def get_user_with_roles(user_id):
    """Get user data with roles included"""
    try:
        user = User.query.get(user_id)
        if not user:
            return None

        user_data = user.to_dict()

        # Get user roles
        roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()
        user_data['roles'] = [role.to_dict() for role in roles]
        user_data['role_names'] = [role.name for role in roles]
        user_data['is_admin'] = any(role.name == 'admin' for role in roles)

        return user_data
    except Exception:
        return None


def check_if_token_revoked(jwt_header, jwt_payload):
    jti = jwt_payload["jti"]
    return jti in jwt_blocklist


def forgot_password(email):
    try:
        if not email or not email.strip():
            return ApiResponse.failure("Email is required", code=400)

        email = email.strip().lower()

        if not is_valid_email(email):
            return ApiResponse.failure("Invalid email format", code=400)

        # Find user by email
        user = get_user_by_email(email)

        # Always return success to prevent email enumeration attacks
        # But only send email if user exists
        if user and user.auth_provider == 'local':
            # Only allow password reset for local users
            if user.password_hash:
                # Generate reset token
                reset_token = PasswordResetToken.generate_token(user.id)

                # Send password reset email
                email_response = EmailService.send_password_reset_email(
                    user.email,
                    user.username,
                    reset_token.token
                )

                if not email_response.success:
                    return email_response

        # Always return success message for security
        return ApiResponse.success(
            "If an account with that email exists, a password reset link has been sent."
        )

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def reset_password(token, new_password):
    try:
        if not token or not new_password:
            return ApiResponse.failure("Token and new password are required", code=400)

        if not is_strong_password(new_password):
            return ApiResponse.failure(
                "Password must be at least 8 characters and include "
                "uppercase, lowercase, number, and special character.",
                code=400,
            )

        # Verify token
        reset_token = PasswordResetToken.verify_token(token)
        if not reset_token:
            return ApiResponse.failure("Invalid or expired reset token", code=400)

        # Get user
        user = get_user_by_id(reset_token.user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        # Check if user is local auth provider
        if user.auth_provider != 'local':
            return ApiResponse.failure(
                "Password reset not available for OAuth users",
                code=400
            )

        # Update password
        user.password_hash = generate_password_hash(new_password)

        # Mark token as used
        reset_token.mark_as_used()

        # Invalidate all existing JWT tokens for this user
        invalidate_all_user_tokens(user.id)

        db.session.commit()

        return ApiResponse.success("Password reset successfully")

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def change_password(user_id, current_password, new_password):
    try:
        if not current_password or not new_password:
            return ApiResponse.failure(
                "Current password and new password are required",
                code=400
            )

        if not is_strong_password(new_password):
            return ApiResponse.failure(
                "Password must be at least 8 characters and include "
                "uppercase, lowercase, number, and special character.",
                code=400,
            )

        # Get user
        user = get_user_by_id(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        # Check if user is local auth provider
        if user.auth_provider != 'local':
            return ApiResponse.failure(
                "Password change not available for OAuth users",
                code=400
            )

        # Check if user has a password set
        if not user.password_hash:
            return ApiResponse.failure(
                "No password set for this account",
                code=400
            )

        # Verify current password
        if not check_password_hash(user.password_hash, current_password):
            return ApiResponse.failure("Current password is incorrect", code=400)

        # Check if new password is different from current
        if check_password_hash(user.password_hash, new_password):
            return ApiResponse.failure(
                "New password must be different from current password",
                code=400
            )

        # Update password
        user.password_hash = generate_password_hash(new_password)
        db.session.commit()

        return ApiResponse.success("Password changed successfully")

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def update_password(user_id, current_password, new_password):
    return change_password(user_id, current_password, new_password)

def reset_session(user_id):
    try:
        # Get user
        user = get_user_by_id(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        # Invalidate all existing JWT tokens for this user
        invalidate_all_user_tokens(user_id)

        return ApiResponse.success("All sessions have been reset successfully")

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def invalidate_all_user_tokens(user_id):
    # Note: This is a simplified implementation
    # In a production environment, you might want to store user-specific token blacklists
    # or implement a more sophisticated token invalidation mechanism

    # For now, we'll add a timestamp-based approach
    # All tokens issued before this timestamp will be considered invalid
    from datetime import datetime
    import time

    # Store the invalidation timestamp for this user
    # This could be stored in Redis or database for better performance
    invalidation_key = f"user_token_invalidation_{user_id}"
    jwt_blocklist[invalidation_key] = int(time.time())

def is_user_token_valid(user_id, token_issued_at):
    invalidation_key = f"user_token_invalidation_{user_id}"
    invalidation_time = jwt_blocklist.get(invalidation_key)

    if invalidation_time is None:
        return True

    return token_issued_at > invalidation_time
