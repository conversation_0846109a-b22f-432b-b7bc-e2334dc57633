"""
GitHub authentication services for user management
"""

from datetime import datetime

from flask_jwt_extended import create_access_token
from app.models.user import User
from app.models.integration import Integration
from app.helpers.extensions import db
from app.models.api_response import ApiResponse


def create_or_update_github_user(github_user_info, access_token):
    """
    Create a new user or update existing user with GitHub information

    Args:
        github_user_info (dict): User information from GitHub
        access_token (str): GitHub access token

    Returns:
        ApiResponse: Response containing user data and JWT token
    """
    try:
        github_id = github_user_info.get("github_id")
        email = github_user_info.get("email")
        github_username = github_user_info.get("username")

        if not github_id or not email:
            return ApiResponse.failure(
                "GitHub user ID and email are required", code=400
            )

        # Check if user exists by GitHub ID
        existing_user = User.query.filter_by(github_id=github_id).first()

        if existing_user:
            # Update existing GitHub user
            existing_user.github_username = github_username
            existing_user.github_avatar_url = github_user_info.get("avatar_url")
            existing_user.updated_at = datetime.utcnow()

            # Update bio if provided and current bio is empty
            if github_user_info.get("bio") and not existing_user.bio:
                existing_user.bio = github_user_info.get("bio")

            user = existing_user
        else:
            # Check if user exists by email
            existing_email_user = User.query.filter_by(email=email).first()

            if existing_email_user:
                # Link GitHub account to existing email user
                existing_email_user.github_id = github_id
                existing_email_user.github_username = github_username
                existing_email_user.github_avatar_url = github_user_info.get(
                    "avatar_url"
                )
                existing_email_user.auth_provider = "github"
                existing_email_user.updated_at = datetime.utcnow()

                # Update bio if provided and current bio is empty
                if github_user_info.get("bio") and not existing_email_user.bio:
                    existing_email_user.bio = github_user_info.get("bio")

                user = existing_email_user
            else:
                # Create new user
                # Generate username if name is available, otherwise use GitHub username
                display_name = github_user_info.get("name") or github_username

                # Ensure username is unique
                base_username = display_name.replace(" ", "_").lower()
                username = base_username
                counter = 1
                while User.query.filter_by(username=username).first():
                    username = f"{base_username}_{counter}"
                    counter += 1

                user = User(
                    username=username,
                    email=email,
                    github_id=github_id,
                    github_username=github_username,
                    github_avatar_url=github_user_info.get("avatar_url"),
                    bio=github_user_info.get("bio"),
                    auth_provider="github",
                    password_hash=None,  # No password for OAuth users
                )

                db.session.add(user)

        # Update or create GitHub integration record
        integration = Integration.query.filter_by(
            user_id=user.id, platform="github"
        ).first()

        if integration:
            integration.access_token = access_token
            integration.platform_user_id = github_id
            integration.updated_at = datetime.utcnow()
            integration.is_active = True
        else:
            integration = Integration(
                user_id=user.id,
                platform="github",
                platform_user_id=github_id,
                access_token=access_token,
                is_active=True,
            )
            db.session.add(integration)

        db.session.commit()

        # Generate JWT token
        jwt_token = create_access_token(identity=str(user.id))

        return ApiResponse.success(
            "GitHub authentication successful",
            data={"access_token": jwt_token, "user": user.to_dict()},
        )

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(
            f"Error creating/updating GitHub user: {str(e)}", code=500
        )


def link_github_account(user_id, github_user_info, access_token):
    """
    Link GitHub account to an existing user

    Args:
        user_id (int): Existing user ID
        github_user_info (dict): User information from GitHub
        access_token (str): GitHub access token

    Returns:
        ApiResponse: Response indicating success or failure
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        github_id = github_user_info.get("github_id")

        # Check if GitHub account is already linked to another user
        existing_github_user = User.query.filter_by(github_id=github_id).first()
        if existing_github_user and existing_github_user.id != user_id:
            return ApiResponse.failure(
                "This GitHub account is already linked to another user", code=400
            )

        # Update user with GitHub information
        user.github_id = github_id
        user.github_username = github_user_info.get("username")
        user.github_avatar_url = github_user_info.get("avatar_url")
        user.updated_at = datetime.utcnow()

        # Update bio if provided and current bio is empty
        if github_user_info.get("bio") and not user.bio:
            user.bio = github_user_info.get("bio")

        # Update or create GitHub integration record
        integration = Integration.query.filter_by(
            user_id=user.id, platform="github"
        ).first()

        if integration:
            integration.access_token = access_token
            integration.platform_user_id = github_id
            integration.updated_at = datetime.utcnow()
            integration.is_active = True
        else:
            integration = Integration(
                user_id=user.id,
                platform="github",
                platform_user_id=github_id,
                access_token=access_token,
                is_active=True,
            )
            db.session.add(integration)

        db.session.commit()

        return ApiResponse.success(
            "GitHub account linked successfully", data=user.to_dict()
        )

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Error linking GitHub account: {str(e)}", code=500)


def unlink_github_account(user_id):
    """
    Unlink GitHub account from user

    Args:
        user_id (int): User ID

    Returns:
        ApiResponse: Response indicating success or failure
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        if not user.github_id:
            return ApiResponse.failure(
                "No GitHub account linked to this user", code=400
            )

        # Check if user has a password (can't unlink if it's the only auth method)
        if not user.password_hash and user.auth_provider == "github":
            return ApiResponse.failure(
                "Cannot unlink GitHub account. Please set a password first.", code=400
            )

        # Remove GitHub information
        user.github_id = None
        user.github_username = None
        user.github_avatar_url = None
        user.updated_at = datetime.utcnow()

        # Reset auth provider if it was GitHub
        if user.auth_provider == "github":
            user.auth_provider = "local"

        # Deactivate GitHub integration
        integration = Integration.query.filter_by(
            user_id=user.id, platform="github"
        ).first()

        if integration:
            integration.is_active = False
            integration.updated_at = datetime.utcnow()

        db.session.commit()

        return ApiResponse.success(
            "GitHub account unlinked successfully", data=user.to_dict()
        )

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(
            f"Error unlinking GitHub account: {str(e)}", code=500
        )
