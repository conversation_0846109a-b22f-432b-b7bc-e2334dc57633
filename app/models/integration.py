"""
External integration related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class Integration(db.Model):
    """
    External platform integration model (e.g., Trello, Asana, Jira)
    """
    __tablename__ = 'integration'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.In<PERSON>ger, db.<PERSON>ey("user.id"), nullable=False)
    platform = db.Column(db.String(50), nullable=False)  # 'trello', 'asana', 'jira', etc.
    platform_user_id = db.Column(db.String(255), nullable=True)
    access_token = db.Column(db.Text, nullable=False)
    refresh_token = db.Column(db.Text, nullable=True)
    expires_at = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    settings = db.Column(db.<PERSON>, nullable=True)  # Platform-specific settings
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    user = db.relationship("User", backref=db.backref("integrations", lazy=True))

    def to_dict(self):
        """Convert integration object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "platform": self.platform,
            "platform_user_id": self.platform_user_id,
            "is_active": self.is_active,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __repr__(self):
        return f"<Integration id={self.id} platform={self.platform}>"


class ExternalTaskMapping(db.Model):
    """
    Mapping between internal tasks and external platform tasks
    """
    __tablename__ = 'external_task_mapping'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey("task.id"), nullable=False)
    integration_id = db.Column(db.Integer, db.ForeignKey("integration.id"), nullable=False)
    external_task_id = db.Column(db.String(255), nullable=False)
    external_project_id = db.Column(db.String(255), nullable=True)
    external_board_id = db.Column(db.String(255), nullable=True)
    sync_mode = db.Column(db.String(50), default="bidirectional")  # 'import', 'export', 'bidirectional'
    sync_status = db.Column(db.String(50), default="active")  # 'active', 'paused', 'error'
    last_synced_at = db.Column(db.DateTime, default=datetime.utcnow)
    sync_errors = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    task = db.relationship("Task", backref=db.backref("external_mappings", lazy=True))
    integration = db.relationship("Integration", backref=db.backref("external_task_mappings", lazy=True))

    def to_dict(self):
        """Convert external task mapping object to dictionary"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "integration_id": self.integration_id,
            "external_task_id": self.external_task_id,
            "external_project_id": self.external_project_id,
            "external_board_id": self.external_board_id,
            "sync_mode": self.sync_mode,
            "sync_status": self.sync_status,
            "last_synced_at": self.last_synced_at.isoformat() if self.last_synced_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    def __repr__(self):
        return f"<ExternalTaskMapping id={self.id} external_id={self.external_task_id}>"
