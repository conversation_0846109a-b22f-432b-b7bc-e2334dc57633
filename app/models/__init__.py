"""
Models package for Task Management System

This package contains all database models organized by functionality:
- user.py: User-related models (User, UserRole)
- task.py: Task-related models (Task, Attachment)
- project.py: Project-related models (Project)
- auth.py: Authentication models (Role, Permission, RolePermission)
- integration.py: External integration models
- system.py: System models (Notification, ActivityLog, Setting)
"""

# Import all models for easy access
from .user import User, UserRole, PasswordResetToken
from .task import Task, Attachment, Comment
from .project import Project, UserProject
from .auth import Role, Permission, RolePermission
from .integration import Integration, ExternalTaskMapping
from .system import Notification, ActivityLog, Setting

# Export all models
__all__ = [
    # User models
    'User',
    'UserRole',
    
    # Task models
    'Task',
    'Attachment',
    'Comment',
    
    # Project models
    'Project',
    'UserProject',
    
    # Auth models
    'Role',
    'Permission',
    'RolePermission',
    
    # Integration models
    'Integration',
    'ExternalTaskMapping',
    
    # System models
    'Notification',
    'ActivityLog',
    'Setting',
]
