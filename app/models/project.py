"""
Project-related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class Project(db.Model):
    """
    Project model for project management
    """
    __tablename__ = 'project'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(50), default='active')  # active, inactive, completed, on_hold
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent
    start_date = db.Column(db.Date, nullable=True)
    end_date = db.Column(db.Date, nullable=True)
    created_by = db.Column(db.Integer, db.<PERSON>ey("user.id"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=True)  # project owner
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)
    github_repo_id = db.Column(db.String(255), nullable=True)
    github_repo_name = db.Column(db.String(255), nullable=True)
    github_repo_url = db.Column(db.String(255), nullable=True)
    github_repo_full_name = db.Column(db.String(255), nullable=True)
    is_github_synced = db.Column(db.Boolean, default=False)
    last_github_sync = db.Column(db.DateTime, nullable=True)

    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_projects')
    owner = db.relationship('User', foreign_keys=[user_id], backref='owned_projects')

    def __repr__(self):
        return f"<Project {self.name}>"

    def to_dict(self):
        """Convert project object to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "priority": self.priority,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "created_by": self.created_by,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
        }

    def is_valid(self) -> tuple[bool, str]:
        """Validate project data"""
        if not self.name or len(self.name.strip()) == 0:
            return False, "Project name is required"

        if len(self.name) > 255:
            return False, "Project name must be less than 255 characters"

        if self.status not in ['active', 'inactive', 'completed', 'on_hold']:
            return False, "Invalid status. Must be one of: active, inactive, completed, on_hold"

        if self.priority not in ['low', 'medium', 'high', 'urgent']:
            return False, "Invalid priority. Must be one of: low, medium, high, urgent"

        if self.start_date and self.end_date and self.start_date > self.end_date:
            return False, "Start date cannot be after end date"

        return True, ""


class UserProject(db.Model):
    """
    Many-to-many relationship between Users and Projects with roles
    """
    __tablename__ = 'user_projects'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='user_projects')
    project = db.relationship('Project', backref='project_members')
    role = db.relationship('Role', backref='user_project_roles')

    # Unique constraint to prevent duplicate user-project combinations
    __table_args__ = (db.UniqueConstraint('user_id', 'project_id', name='unique_user_project'),)

    def to_dict(self):
        """Convert user project object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "project_id": self.project_id,
            "role_id": self.role_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def __repr__(self):
        return f"<UserProject user_id={self.user_id} project_id={self.project_id} role_id={self.role_id}>"
