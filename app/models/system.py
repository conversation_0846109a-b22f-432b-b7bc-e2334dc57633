"""
System-related models for Task Management System
"""

from app.helpers.extensions import db
from datetime import datetime


class Notification(db.Model):
    """
    User notification model
    """
    __tablename__ = 'notification'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey("user.id"), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), default="info")  # 'info', 'warning', 'error', 'success'
    priority = db.Column(db.String(20), default="normal")  # 'low', 'normal', 'high', 'urgent'
    is_read = db.Column(db.Boole<PERSON>, default=False)
    read_at = db.Column(db.DateTime, nullable=True)
    related_entity_type = db.Column(db.String(50), nullable=True)  # 'task', 'project', 'user'
    related_entity_id = db.Column(db.Integer, nullable=True)
    action_url = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    user = db.relationship("User", backref=db.backref("notifications", lazy=True))

    def to_dict(self):
        """Convert notification object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "title": self.title,
            "message": self.message,
            "type": self.type,
            "priority": self.priority,
            "is_read": self.is_read,
            "read_at": self.read_at.isoformat() if self.read_at else None,
            "related_entity_type": self.related_entity_type,
            "related_entity_id": self.related_entity_id,
            "action_url": self.action_url,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        self.read_at = datetime.utcnow()

    def __repr__(self):
        return f"<Notification id={self.id} user_id={self.user_id}>"


class ActivityLog(db.Model):
    """
    System activity log for audit trail
    """
    __tablename__ = 'activity_log'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey("project.id"), nullable=True)
    task_id = db.Column(db.Integer, db.ForeignKey("task.id"), nullable=True)
    action = db.Column(db.String(50), nullable=False)  # 'create', 'update', 'delete', 'login', etc.
    entity_type = db.Column(db.String(50), nullable=False)  # 'task', 'project', 'user', 'system'
    entity_id = db.Column(db.Integer, nullable=True)
    description = db.Column(db.Text, nullable=False)
    details = db.Column(db.JSON, nullable=True)  # Additional structured data
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship("User", backref=db.backref("activity_logs", lazy=True))
    project = db.relationship("Project", backref=db.backref("activity_logs", lazy=True))
    task = db.relationship("Task", backref=db.backref("activity_logs", lazy=True))

    def to_dict(self):
        """Convert activity log object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "project_id": self.project_id,
            "task_id": self.task_id,
            "action": self.action,
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "description": self.description,
            "details": self.details,
            "ip_address": self.ip_address,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    def __repr__(self):
        return f"<ActivityLog id={self.id} action={self.action}>"


class Setting(db.Model):
    """
    Application settings (user-specific and global)
    """
    __tablename__ = 'setting'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=True)
    category = db.Column(db.String(50), nullable=False)  # 'ui', 'notification', 'integration', etc.
    key = db.Column(db.String(100), nullable=False)
    value = db.Column(db.Text, nullable=False)
    data_type = db.Column(db.String(20), default="string")  # 'string', 'integer', 'boolean', 'json'
    is_global = db.Column(db.Boolean, default=False)
    is_encrypted = db.Column(db.Boolean, default=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship("User", backref=db.backref("settings", lazy=True))

    def to_dict(self):
        """Convert setting object to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "category": self.category,
            "key": self.key,
            "value": self.value,
            "data_type": self.data_type,
            "is_global": self.is_global,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def get_typed_value(self):
        """Get value converted to appropriate type"""
        if self.data_type == "integer":
            return int(self.value)
        elif self.data_type == "boolean":
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.data_type == "json":
            import json
            return json.loads(self.value)
        else:
            return self.value

    def __repr__(self):
        return f"<Setting id={self.id} key={self.key}>"
