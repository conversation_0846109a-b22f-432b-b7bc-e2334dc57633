class ApiResponse:
    def __init__(self, is_success: bool, message: str = "", data=None, code: int = 200):
        self.is_success = is_success
        self.message = message
        self.data = data
        self.code = code

    def to_dict(self):
        return {
            "success": self.is_success,
            "message": self.message,
            "data": self.data,
            "code": self.code,
        }

    @classmethod
    def success(cls, message: str = "Success", data=None, code: int = 200):
        return cls(is_success=True, message=message, data=data, code=code)

    @classmethod
    def failure(cls, message: str = "Failure", data=None, code: int = 400):
        return cls(is_success=False, message=message, data=data, code=code)
