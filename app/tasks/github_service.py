"""
GitHub issues service for task synchronization
"""

import requests
from datetime import datetime
from typing import List, Dict, Optional
from app.models.user import User
from app.models.project import Project
from app.models.task import Task
from app.models.integration import Integration
from app.helpers.extensions import db
from app.models.api_response import ApiResponse


class GitHubIssuesService:
    """Service for GitHub issues operations"""

    GITHUB_API_BASE_URL = "https://api.github.com"
    
    def __init__(self, user_id: int):
        """
        Initialize GitHub issues service for a specific user
        
        Args:
            user_id (int): User ID
        """
        self.user_id = user_id
        self.user = User.query.get(user_id)
        if not self.user:
            raise ValueError(f"User with ID {user_id} not found")
        
        # Get GitHub integration
        self.github_integration = Integration.query.filter_by(
            user_id=user_id,
            platform="github",
            is_active=True
        ).first()
        
        if not self.github_integration:
            raise ValueError("No active GitHub integration found for user")
        
        self.headers = {
            "Authorization": f"Bearer {self.github_integration.access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "TMS-Python-Backend",
        }

    def get_repository_issues(self, owner: str, repo: str, state: str = "all", 
                            per_page: int = 100, page: int = 1) -> ApiResponse:
        """
        Get issues for a specific repository
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            state (str): Issue state (open, closed, all)
            per_page (int): Number of issues per page (max 100)
            page (int): Page number
            
        Returns:
            ApiResponse: Response containing issues data or error
        """
        try:
            url = f"{self.GITHUB_API_BASE_URL}/repos/{owner}/{repo}/issues"
            params = {
                "state": state,
                "per_page": min(per_page, 100),
                "page": page,
                "sort": "updated",
                "direction": "desc"
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code != 200:
                return ApiResponse.failure(
                    f"Failed to fetch issues: {response.status_code}",
                    code=response.status_code
                )
            
            issues = response.json()
            
            # Transform issue data to include only needed fields
            issue_data = []
            for issue in issues:
                # Skip pull requests (they appear in issues API)
                if issue.get("pull_request"):
                    continue
                    
                issue_info = {
                    "id": str(issue.get("id")),
                    "number": issue.get("number"),
                    "title": issue.get("title"),
                    "body": issue.get("body"),
                    "state": issue.get("state"),
                    "html_url": issue.get("html_url"),
                    "created_at": issue.get("created_at"),
                    "updated_at": issue.get("updated_at"),
                    "closed_at": issue.get("closed_at"),
                    "labels": [
                        {
                            "name": label.get("name"),
                            "color": label.get("color"),
                            "description": label.get("description")
                        }
                        for label in issue.get("labels", [])
                    ],
                    "assignees": [
                        {
                            "login": assignee.get("login"),
                            "id": assignee.get("id"),
                            "avatar_url": assignee.get("avatar_url")
                        }
                        for assignee in issue.get("assignees", [])
                    ],
                    "user": {
                        "login": issue.get("user", {}).get("login"),
                        "id": issue.get("user", {}).get("id"),
                        "avatar_url": issue.get("user", {}).get("avatar_url")
                    },
                    "milestone": {
                        "title": issue.get("milestone", {}).get("title"),
                        "description": issue.get("milestone", {}).get("description"),
                        "due_on": issue.get("milestone", {}).get("due_on")
                    } if issue.get("milestone") else None
                }
                issue_data.append(issue_info)
            
            return ApiResponse.success(
                "Issues fetched successfully",
                data={
                    "issues": issue_data,
                    "total_count": len(issue_data),
                    "page": page,
                    "per_page": per_page,
                    "repository": f"{owner}/{repo}"
                }
            )
            
        except requests.RequestException as e:
            return ApiResponse.failure(
                f"Network error while fetching issues: {str(e)}",
                code=500
            )
        except Exception as e:
            return ApiResponse.failure(
                f"Unexpected error while fetching issues: {str(e)}",
                code=500
            )

    def get_all_repository_issues(self, owner: str, repo: str, state: str = "all") -> ApiResponse:
        """
        Fetch all issues for a repository from GitHub
        
        Args:
            owner (str): Repository owner
            repo (str): Repository name
            state (str): Issue state (open, closed, all)
            
        Returns:
            ApiResponse: Response containing all issues data or error
        """
        try:
            all_issues = []
            page = 1
            per_page = 100
            
            while True:
                response = self.get_repository_issues(
                    owner=owner,
                    repo=repo,
                    state=state,
                    per_page=per_page,
                    page=page
                )
                
                if not response.success:
                    return response
                
                issues = response.data.get("issues", [])
                if not issues:
                    break
                
                all_issues.extend(issues)
                
                # If we got less than per_page, we've reached the end
                if len(issues) < per_page:
                    break
                
                page += 1
            
            return ApiResponse.success(
                "All issues fetched successfully",
                data={
                    "issues": all_issues,
                    "total_count": len(all_issues),
                    "repository": f"{owner}/{repo}"
                }
            )
            
        except Exception as e:
            return ApiResponse.failure(
                f"Error fetching issues: {str(e)}",
                code=500
            )

    def sync_repository_issues_to_tasks(self, project_id: int) -> ApiResponse:
        """
        Sync GitHub issues from a repository to local tasks
        
        Args:
            project_id (int): Local project ID
            
        Returns:
            ApiResponse: Response containing sync results
        """
        try:
            # Get the project
            project = Project.query.filter_by(
                id=project_id,
                user_id=self.user_id,
                deleted_at=None,
                is_github_synced=True
            ).first()
            
            if not project:
                return ApiResponse.failure(
                    "Project not found or not synced from GitHub",
                    code=404
                )
            
            if not project.github_repo_full_name:
                return ApiResponse.failure(
                    "Project does not have GitHub repository information",
                    code=400
                )
            
            # Extract owner and repo name
            owner, repo_name = project.github_repo_full_name.split("/")
            
            # Fetch all issues from GitHub
            issues_response = self.get_all_repository_issues(owner, repo_name, state="all")
            if not issues_response.success:
                return issues_response
            
            all_issues = issues_response.data.get("issues", [])
            
            # Sync issues to tasks
            synced_tasks = []
            errors = []
            
            for issue in all_issues:
                try:
                    # Check if task already exists
                    existing_task = Task.query.filter_by(
                        github_issue_id=issue["id"],
                        project_id=project_id,
                        deleted_at=None
                    ).first()
                    
                    # Map GitHub issue state to task status
                    status_mapping = {
                        "open": "To Do",
                        "closed": "Done"
                    }
                    task_status = status_mapping.get(issue["state"], "To Do")
                    
                    # Parse due date from milestone if available
                    due_date = None
                    if issue.get("milestone") and issue["milestone"].get("due_on"):
                        try:
                            due_date = datetime.fromisoformat(
                                issue["milestone"]["due_on"].replace("Z", "+00:00")
                            )
                        except:
                            pass
                    
                    if existing_task:
                        # Update existing task
                        existing_task.title = issue["title"]
                        existing_task.description = issue["body"] or ""
                        existing_task.status = task_status
                        existing_task.due_date = due_date
                        existing_task.github_issue_number = issue["number"]
                        existing_task.github_issue_url = issue["html_url"]
                        existing_task.last_github_sync = datetime.utcnow()
                        existing_task.updated_at = datetime.utcnow()
                        
                        synced_tasks.append({
                            "action": "updated",
                            "task": existing_task.to_dict()
                        })
                    else:
                        # Create new task
                        new_task = Task(
                            title=issue["title"],
                            description=issue["body"] or "",
                            status=task_status,
                            priority="Medium",
                            project_id=project_id,
                            created_by=self.user_id,
                            due_date=due_date,
                            github_issue_id=issue["id"],
                            github_issue_number=issue["number"],
                            github_issue_url=issue["html_url"],
                            is_github_synced=True,
                            last_github_sync=datetime.utcnow()
                        )
                        
                        db.session.add(new_task)
                        db.session.flush()  # Get the ID
                        
                        synced_tasks.append({
                            "action": "created",
                            "task": new_task.to_dict()
                        })
                
                except Exception as e:
                    errors.append(f"Error syncing issue #{issue.get('number', 'unknown')}: {str(e)}")
            
            # Update project's last sync time
            project.last_github_sync = datetime.utcnow()
            project.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            return ApiResponse.success(
                "Issues synced successfully",
                data={
                    "synced_tasks": synced_tasks,
                    "total_synced": len(synced_tasks),
                    "total_issues": len(all_issues),
                    "errors": errors,
                    "project": project.to_dict()
                }
            )
            
        except Exception as e:
            db.session.rollback()
            return ApiResponse.failure(
                f"Error syncing issues: {str(e)}",
                code=500
            )
