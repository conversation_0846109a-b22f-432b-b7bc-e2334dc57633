from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import get_all_tasks, create_task, update_task, delete_task
from .github_service import GitHubIssuesService
from app.models.api_response import ApiResponse

tasks_bp = Blueprint("tasks_bp", __name__, url_prefix="/tasks")


@tasks_bp.route("/list_tasks", methods=["GET"])
@jwt_required()
def list_tasks():
    current_user_id = get_jwt_identity()
    assignee_id = request.args.get("assignee_id", type=int)
    response = get_all_tasks(current_user_id, assignee_id)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/create_task_view", methods=["POST"])
@jwt_required()
def create_task_view():
    current_user_id = get_jwt_identity()
    data = request.get_json()

    response = create_task(data, current_user_id)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/update_task_view/<int:task_id>", methods=["PUT"])
@jwt_required()
def update_task_view(task_id):
    current_user_id = get_jwt_identity()
    data = request.get_json()
    response = update_task(task_id, data, current_user_id)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/delete_task_view/<int:task_id>", methods=["DELETE"])
@jwt_required()
def delete_task_view(task_id):
    current_user_id = get_jwt_identity()
    response = delete_task(task_id, current_user_id)
    return jsonify(response.to_dict()), response.code


@tasks_bp.route("/github/sync/<int:project_id>", methods=["POST"])
@jwt_required()
def sync_github_issues(project_id):
    """Sync GitHub issues from a repository to local tasks"""
    try:
        current_user_id = int(get_jwt_identity())
        
        github_service = GitHubIssuesService(current_user_id)
        response = github_service.sync_repository_issues_to_tasks(project_id)

        return jsonify(response.to_dict()), response.code

    except ValueError as e:
        response = ApiResponse.failure(str(e), code=400)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Error syncing issues: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code
