from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from .services import (
    get_all_users,
    get_user_roles_service,
    get_user_permissions
)
from app.models.api_response import ApiResponse

users_bp = Blueprint("users_bp", __name__, url_prefix="/users")



@users_bp.route("", methods=["GET"])
@jwt_required()
def get_users():
    """Get all users with pagination and search (admin only)"""
    try:
        current_user_id = int(get_jwt_identity())
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', None)

        # Limit per_page to prevent abuse
        per_page = min(per_page, 100)

        response = get_all_users(current_user_id, page, per_page, search)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@users_bp.route("/<int:user_id>/roles", methods=["GET"])
@jwt_required()
def get_user_roles_endpoint(user_id):
    """Get user roles (admin and project_owner only)"""
    try:
        current_user_id = int(get_jwt_identity())

        # Check permission - only admin, project_owner, and project_manager can view user roles
        from .services import get_user_roles
        current_user_roles = get_user_roles(current_user_id)
        if not ('admin' in current_user_roles or 'project_owner' in current_user_roles or 'project_manager' in current_user_roles):
            response = ApiResponse.failure("Permission denied", code=403)
            return jsonify(response.to_dict()), response.code

        response = get_user_roles_service(user_id)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code


@users_bp.route("/<int:user_id>/permissions", methods=["GET"])
@jwt_required()
def get_user_permissions_endpoint(user_id):
    """Get user permissions (admin and project_owner only)"""
    try:
        current_user_id = int(get_jwt_identity())

        # Check permission - only admin, project_owner, and project_manager can view user permissions
        from .services import get_user_roles
        current_user_roles = get_user_roles(current_user_id)
        if not ('admin' in current_user_roles or 'project_owner' in current_user_roles or 'project_manager' in current_user_roles):
            response = ApiResponse.failure("Permission denied", code=403)
            return jsonify(response.to_dict()), response.code

        response = get_user_permissions(user_id)
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code

@users_bp.route("/<int:user_id>/roles", methods=["POST"])
@jwt_required()
def assign_role_to_user(user_id):
    """Assign role to user (admin and project_owner with restrictions)"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        if not data or 'role_name' not in data:
            response = ApiResponse.failure("Role name is required", code=400)
            return jsonify(response.to_dict()), response.code

        response = assign_user_role(current_user_id, user_id, data['role_name'])
        return jsonify(response.to_dict()), response.code
    except Exception as e:
        response = ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
        return jsonify(response.to_dict()), response.code