from sqlalchemy import or_
from app.models.api_response import ApiResponse
from app.models.user import User, UserRole
from app.models.auth import Role, Permission, RolePermission
from app.helpers.extensions import db

def get_user_roles(user_id):
    """Get all role names for a user"""
    try:
        roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()
        return [role.name for role in roles]
    except Exception:
        return []

def get_all_users(current_user_id, page=1, per_page=20, search=None):
    """Get all users with pagination and search (admin, project_owner, and project_manager only)"""
    try:
        # Check if current user has permission
        current_user_roles = get_user_roles(current_user_id)
        if not ('admin' in current_user_roles or 'project_owner' in current_user_roles or 'project_manager' in current_user_roles):
            return ApiResponse.failure("Permission denied", code=403)

        query = User.query

        # Apply search filter
        if search:
            query = query.filter(
                or_(
                    User.username.ilike(f'%{search}%'),
                    User.email.ilike(f'%{search}%')
                )
            )

        # Apply pagination
        users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        users_data = []
        for user in users.items:
            user_data = user.to_dict()
            # Get user roles
            user_roles = db.session.query(Role).join(UserRole).filter(
                UserRole.user_id == user.id
            ).all()
            user_data['roles'] = [role.to_dict() for role in user_roles]
            users_data.append(user_data)

        response_data = {
            'users': users_data,
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total,
                'has_next': users.has_next,
                'has_prev': users.has_prev
            }
        }

        return ApiResponse.success("Users retrieved successfully", data=response_data)

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)

def get_user_roles_service(user_id):
    """Get all roles for a user - service endpoint"""
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()

        roles_data = [role.to_dict() for role in roles]

        return ApiResponse.success("User roles retrieved successfully", data=roles_data)

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)

def get_user_permissions(user_id):
    """Get all permissions for a user based on their roles"""
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        # Get all permissions through user roles
        permissions = db.session.query(Permission).join(RolePermission).join(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).distinct().all()

        permissions_data = [permission.to_dict() for permission in permissions]

        return ApiResponse.success("User permissions retrieved successfully", data=permissions_data)

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)

def assign_user_role(current_user_id, target_user_id, role_name):
    """Assign role to user with permission checking"""
    try:
        # Check if target user exists
        target_user = User.query.get(target_user_id)
        if not target_user:
            return ApiResponse.failure("User not found", code=404)

        # Check if role exists
        role = Role.query.filter_by(name=role_name).first()
        if not role:
            return ApiResponse.failure("Role not found", code=404)

        # Get current user roles
        current_user_roles = get_user_roles(current_user_id)
        target_user_roles = get_user_roles(target_user_id)

        # Permission checking based on current user role
        if 'admin' in current_user_roles:
            # Admin can set any role except admin role
            if role_name == 'admin':
                return ApiResponse.failure("Admin cannot assign admin role to other users", code=403)
        elif 'project_owner' in current_user_roles:
            # Project owner restrictions
            # Cannot assign roles to admin, project_owner, or project_manager users
            if 'admin' in target_user_roles or 'project_owner' in target_user_roles or 'project_manager' in target_user_roles:
                return ApiResponse.failure("Project owner cannot assign roles to admin, project owner, or project manager users", code=403)

            # Can only assign project_member or project_viewer roles
            if role_name not in ['project_member', 'project_viewer']:
                return ApiResponse.failure("Project owner can only assign project_member or project_viewer roles", code=403)
        elif 'project_manager' in current_user_roles:
            # Project manager restrictions (similar to project_owner)
            # Cannot assign roles to admin, project_owner, or project_manager users
            if 'admin' in target_user_roles or 'project_owner' in target_user_roles or 'project_manager' in target_user_roles:
                return ApiResponse.failure("Project manager cannot assign roles to admin, project owner, or project manager users", code=403)

            # Can only assign project_member or project_viewer roles
            if role_name not in ['project_member', 'project_viewer']:
                return ApiResponse.failure("Project manager can only assign project_member or project_viewer roles", code=403)
        else:
            # Other users cannot assign roles
            return ApiResponse.failure("Permission denied", code=403)

        # Check if user already has this role
        existing_user_role = UserRole.query.filter_by(
            user_id=target_user_id,
            role_id=role.id
        ).first()

        if existing_user_role:
            return ApiResponse.failure("User already has this role", code=400)

        # Assign role
        user_role = UserRole(user_id=target_user_id, role_id=role.id)
        db.session.add(user_role)
        db.session.commit()

        return ApiResponse.success(f"Role '{role_name}' assigned to user successfully")

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)

def remove_user_role(current_user_id, target_user_id, role_name):
    """Remove role from user with permission checking"""
    try:
        # Check if target user exists
        target_user = User.query.get(target_user_id)
        if not target_user:
            return ApiResponse.failure("User not found", code=404)

        # Check if role exists
        role = Role.query.filter_by(name=role_name).first()
        if not role:
            return ApiResponse.failure("Role not found", code=404)

        # Get current user roles
        current_user_roles = get_user_roles(current_user_id)
        target_user_roles = get_user_roles(target_user_id)

        # Permission checking based on current user role
        if 'admin' in current_user_roles:
            # Admin can remove any role except admin role
            if role_name == 'admin':
                return ApiResponse.failure("Admin cannot remove admin role from other users", code=403)
        elif 'project_owner' in current_user_roles:
            # Project owner restrictions
            # Cannot remove roles from admin, project_owner, or project_manager users
            if 'admin' in target_user_roles or 'project_owner' in target_user_roles or 'project_manager' in target_user_roles:
                return ApiResponse.failure("Project owner cannot remove roles from admin, project owner, or project manager users", code=403)

            # Can only remove project_member or project_viewer roles
            if role_name not in ['project_member', 'project_viewer']:
                return ApiResponse.failure("Project owner can only remove project_member or project_viewer roles", code=403)
        elif 'project_manager' in current_user_roles:
            # Project manager restrictions (similar to project_owner)
            # Cannot remove roles from admin, project_owner, or project_manager users
            if 'admin' in target_user_roles or 'project_owner' in target_user_roles or 'project_manager' in target_user_roles:
                return ApiResponse.failure("Project manager cannot remove roles from admin, project owner, or project manager users", code=403)

            # Can only remove project_member or project_viewer roles
            if role_name not in ['project_member', 'project_viewer']:
                return ApiResponse.failure("Project manager can only remove project_member or project_viewer roles", code=403)
        else:
            # Other users cannot remove roles
            return ApiResponse.failure("Permission denied", code=403)

        # Find and remove user role
        user_role = UserRole.query.filter_by(
            user_id=target_user_id,
            role_id=role.id
        ).first()

        if not user_role:
            return ApiResponse.failure("User does not have this role", code=400)

        db.session.delete(user_role)
        db.session.commit()

        return ApiResponse.success(f"Role '{role_name}' removed from user successfully")

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)
