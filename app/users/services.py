from werkzeug.security import generate_password_hash
from sqlalchemy import or_
from app.models.api_response import ApiResponse
from app.models.user import User, UserRole
from app.models.auth import Role, Permission, RolePermission
from app.helpers.extensions import db
from app.helpers.validators import is_valid_email


def get_user_profile(user_id):
    """Get user profile by ID"""
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        # Get user roles
        user_roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()

        user_data = user.to_dict()
        user_data['roles'] = [role.to_dict() for role in user_roles]

        return ApiResponse.success("User profile retrieved successfully", data=user_data)

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def update_user_profile(user_id, data):
    """Update user profile"""
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        # Validate email if provided
        if 'email' in data and data['email']:
            if not is_valid_email(data['email']):
                return ApiResponse.failure("Invalid email format", code=400)

            # Check if email already exists for another user
            existing_user = User.query.filter(
                User.email == data['email'],
                User.id != user_id
            ).first()
            if existing_user:
                return ApiResponse.failure("Email already exists", code=400)

        # Update allowed fields
        allowed_fields = ['username', 'email', 'phone', 'bio']
        for field in allowed_fields:
            if field in data:
                setattr(user, field, data[field])

        db.session.commit()

        return ApiResponse.success("Profile updated successfully", data=user.to_dict())

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def get_all_users(current_user_id, page=1, per_page=20, search=None):
    """Get all users with pagination and search (admin only)"""
    try:
        # Check if current user is admin
        if not is_admin(current_user_id):
            return ApiResponse.failure("Permission denied", code=403)

        query = User.query

        # Apply search filter
        if search:
            query = query.filter(
                or_(
                    User.username.ilike(f'%{search}%'),
                    User.email.ilike(f'%{search}%')
                )
            )

        # Apply pagination
        users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        users_data = []
        for user in users.items:
            user_data = user.to_dict()
            # Get user roles
            user_roles = db.session.query(Role).join(UserRole).filter(
                UserRole.user_id == user.id
            ).all()
            user_data['roles'] = [role.to_dict() for role in user_roles]
            users_data.append(user_data)

        response_data = {
            'users': users_data,
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total,
                'has_next': users.has_next,
                'has_prev': users.has_prev
            }
        }

        return ApiResponse.success("Users retrieved successfully", data=response_data)

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def assign_user_role(current_user_id, target_user_id, role_name):
    """Assign role to user (admin only)"""
    try:
        # Check if current user is admin
        if not is_admin(current_user_id):
            return ApiResponse.failure("Permission denied", code=403)

        # Check if target user exists
        target_user = User.query.get(target_user_id)
        if not target_user:
            return ApiResponse.failure("User not found", code=404)

        # Check if role exists
        role = Role.query.filter_by(name=role_name).first()
        if not role:
            return ApiResponse.failure("Role not found", code=404)

        # Check if user already has this role
        existing_user_role = UserRole.query.filter_by(
            user_id=target_user_id,
            role_id=role.id
        ).first()

        if existing_user_role:
            return ApiResponse.failure("User already has this role", code=400)

        # Assign role
        user_role = UserRole(user_id=target_user_id, role_id=role.id)
        db.session.add(user_role)
        db.session.commit()

        return ApiResponse.success(f"Role '{role_name}' assigned to user successfully")

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def remove_user_role(current_user_id, target_user_id, role_name):
    """Remove role from user (admin only)"""
    try:
        # Check if current user is admin
        if not is_admin(current_user_id):
            return ApiResponse.failure("Permission denied", code=403)

        # Check if target user exists
        target_user = User.query.get(target_user_id)
        if not target_user:
            return ApiResponse.failure("User not found", code=404)

        # Check if role exists
        role = Role.query.filter_by(name=role_name).first()
        if not role:
            return ApiResponse.failure("Role not found", code=404)

        # Find and remove user role
        user_role = UserRole.query.filter_by(
            user_id=target_user_id,
            role_id=role.id
        ).first()

        if not user_role:
            return ApiResponse.failure("User does not have this role", code=400)

        db.session.delete(user_role)
        db.session.commit()

        return ApiResponse.success(f"Role '{role_name}' removed from user successfully")

    except Exception as e:
        db.session.rollback()
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def is_admin(user_id):
    """Check if user has admin role"""
    try:
        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            return False

        user_role = UserRole.query.filter_by(
            user_id=user_id,
            role_id=admin_role.id
        ).first()

        return user_role is not None

    except Exception:
        return False


def get_user_roles(user_id):
    """Get all roles for a user"""
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        roles = db.session.query(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).all()

        roles_data = [role.to_dict() for role in roles]

        return ApiResponse.success("User roles retrieved successfully", data=roles_data)

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)


def get_user_permissions(user_id):
    """Get all permissions for a user based on their roles"""
    try:
        user = User.query.get(user_id)
        if not user:
            return ApiResponse.failure("User not found", code=404)

        # Get all permissions through user roles
        permissions = db.session.query(Permission).join(RolePermission).join(Role).join(UserRole).filter(
            UserRole.user_id == user_id
        ).distinct().all()

        permissions_data = [permission.to_dict() for permission in permissions]

        return ApiResponse.success("User permissions retrieved successfully", data=permissions_data)

    except Exception as e:
        return ApiResponse.failure(f"Internal server error: {str(e)}", code=500)