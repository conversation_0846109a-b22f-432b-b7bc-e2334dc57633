import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models import User
from datetime import datetime

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def print_user_details(user):
    print(f"ID: {user.id}, Username: {user.username}, Email: {user.email}")
    print(f"  Phone: {user.phone}")
    print(f"  Bio: {user.bio}")
    print(f"  Created At: {user.created_at}")
    print(f"  Updated At: {user.updated_at}")
    print(f"  Deleted At: {user.deleted_at}")
    print("-------------------")

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        # Show all users before update
        print("USERS BEFORE UPDATE:")
        users = User.query.all()
        print(f"Found {len(users)} users:")
        for user in users:
            print_user_details(user)

        # Update a specific user (let's update user with ID 1)
        target_user = User.query.get(1)
        if target_user:
            print(f"\nUPDATING USER: {target_user.username} (ID: {target_user.id})")
            # Update fields
            target_user.phone = "******-123-4567"
            target_user.bio = "This is a test bio added during the update process."
            
            # Save changes
            db.session.commit()
            
            # Fetch the updated user
            updated_user = User.query.get(1)
            print("\nUPDATED USER DETAILS:")
            print_user_details(updated_user)
            
            print("\nNote: updated_at should now reflect the time of update")
            
        # Demonstrate soft delete
        print("\nDEMONSTRATING SOFT DELETE:")
        target_user = User.query.get(2)
        if target_user:
            print(f"Soft deleting user: {target_user.username} (ID: {target_user.id})")
            # Set deleted_at timestamp
            target_user.deleted_at = datetime.utcnow()
            db.session.commit()
            
            # Fetch the soft-deleted user
            deleted_user = User.query.get(2)
            print("\nSOFT-DELETED USER DETAILS:")
            print_user_details(deleted_user)
            
            print("In a real application, you would typically filter out soft-deleted users with:")
            print("User.query.filter(User.deleted_at.is_(None)).all()")
