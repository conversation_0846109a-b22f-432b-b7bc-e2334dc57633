import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models import Role, User

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        # Check roles
        roles = Role.query.all()
        
        print(f"Found {len(roles)} roles:")
        for role in roles:
            print(f"ID: {role.id}, Name: {role.name}")
            print(f"  Description: {role.description}")
            print(f"  Created At: {role.created_at}")
            print(f"  Updated At: {role.updated_at}")
            print(f"  Created By: {role.created_by}")
            print(f"  Updated By: {role.updated_by}")
            print("-------------------")
        
        # Check users
        users = User.query.all()
        print(f"\nFound {len(users)} users:")
        for user in users:
            print(f"ID: {user.id}, Username: {user.username}, Email: {user.email}")
            print(f"  Phone: {user.phone}")
            print(f"  Bio: {user.bio}")
            print(f"  Created At: {user.created_at}")
            print(f"  Updated At: {user.updated_at}")
            print(f"  Deleted At: {user.deleted_at}")
            print("-------------------")
