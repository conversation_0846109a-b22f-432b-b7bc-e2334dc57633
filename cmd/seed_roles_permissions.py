#!/usr/bin/env python3
"""
Seed script for creating default roles and permissions
"""

import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.helpers.extensions import db
from app.models.auth import Role, Permission, RolePermission
from app.models.user import User, UserRole


def create_default_permissions():
    """Create default system permissions"""
    permissions = [
        # User management permissions
        {
            'name': 'manage_users',
            'description': 'Create, update, delete users',
            'resource': 'user',
            'action': 'manage'
        },
        {
            'name': 'view_users',
            'description': 'View user profiles and lists',
            'resource': 'user',
            'action': 'read'
        },
        {
            'name': 'update_own_profile',
            'description': 'Update own user profile',
            'resource': 'user',
            'action': 'update_own'
        },
        
        # Role and permission management
        {
            'name': 'manage_roles',
            'description': 'Create, update, delete roles',
            'resource': 'role',
            'action': 'manage'
        },
        {
            'name': 'assign_roles',
            'description': 'Assign roles to users',
            'resource': 'role',
            'action': 'assign'
        },
        
        # Project management permissions
        {
            'name': 'create_project',
            'description': 'Create new projects',
            'resource': 'project',
            'action': 'create'
        },
        {
            'name': 'view_project',
            'description': 'View project details',
            'resource': 'project',
            'action': 'read'
        },
        {
            'name': 'edit_project',
            'description': 'Edit project details',
            'resource': 'project',
            'action': 'update'
        },
        {
            'name': 'delete_project',
            'description': 'Delete projects',
            'resource': 'project',
            'action': 'delete'
        },
        {
            'name': 'manage_project_members',
            'description': 'Add/remove project members',
            'resource': 'project',
            'action': 'manage_members'
        },
        
        # Task management permissions
        {
            'name': 'create_task',
            'description': 'Create new tasks',
            'resource': 'task',
            'action': 'create'
        },
        {
            'name': 'view_task',
            'description': 'View task details',
            'resource': 'task',
            'action': 'read'
        },
        {
            'name': 'edit_task',
            'description': 'Edit task details',
            'resource': 'task',
            'action': 'update'
        },
        {
            'name': 'delete_task',
            'description': 'Delete tasks',
            'resource': 'task',
            'action': 'delete'
        },
        {
            'name': 'assign_task',
            'description': 'Assign tasks to users',
            'resource': 'task',
            'action': 'assign'
        },
        
        # Comment permissions
        {
            'name': 'add_comment',
            'description': 'Add comments to tasks',
            'resource': 'comment',
            'action': 'create'
        },
        {
            'name': 'edit_comment',
            'description': 'Edit comments',
            'resource': 'comment',
            'action': 'update'
        },
        {
            'name': 'delete_comment',
            'description': 'Delete comments',
            'resource': 'comment',
            'action': 'delete'
        },
        
        # System permissions
        {
            'name': 'view_statistics',
            'description': 'View system statistics',
            'resource': 'system',
            'action': 'view_stats'
        },
        {
            'name': 'manage_system',
            'description': 'Manage system settings',
            'resource': 'system',
            'action': 'manage'
        }
    ]
    
    created_permissions = []
    for perm_data in permissions:
        existing = Permission.query.filter_by(name=perm_data['name']).first()
        if not existing:
            permission = Permission(**perm_data)
            db.session.add(permission)
            created_permissions.append(perm_data['name'])
    
    return created_permissions


def create_default_roles():
    """Create default system roles"""
    roles = [
        {
            'name': 'admin',
            'description': 'System administrator with full access',
            'created_by': 'system'
        },
        {
            'name': 'member',
            'description': 'Regular user with basic permissions',
            'created_by': 'system'
        }
    ]
    
    created_roles = []
    for role_data in roles:
        existing = Role.query.filter_by(name=role_data['name']).first()
        if not existing:
            role = Role(**role_data)
            db.session.add(role)
            created_roles.append(role_data['name'])
    
    return created_roles


def assign_permissions_to_roles():
    """Assign permissions to default roles"""
    # Admin gets all permissions
    admin_role = Role.query.filter_by(name='admin').first()
    if admin_role:
        all_permissions = Permission.query.all()
        for permission in all_permissions:
            existing = RolePermission.query.filter_by(
                role_id=admin_role.id,
                permission_id=permission.id
            ).first()
            if not existing:
                role_perm = RolePermission(
                    role_id=admin_role.id,
                    permission_id=permission.id
                )
                db.session.add(role_perm)
    
    # Member gets limited permissions
    member_role = Role.query.filter_by(name='member').first()
    if member_role:
        member_permissions = [
            'update_own_profile',
            'view_project',
            'create_task',
            'view_task',
            'edit_task',
            'add_comment',
            'edit_comment'
        ]
        
        for perm_name in member_permissions:
            permission = Permission.query.filter_by(name=perm_name).first()
            if permission:
                existing = RolePermission.query.filter_by(
                    role_id=member_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=member_role.id,
                        permission_id=permission.id
                    )
                    db.session.add(role_perm)


def check_admin_users():
    """Check if any admin user exists"""
    admin_role = Role.query.filter_by(name='admin').first()
    if not admin_role:
        print("❌ Admin role not found.")
        return

    # Check if any admin user exists
    admin_users = db.session.query(User).join(UserRole).filter(
        UserRole.role_id == admin_role.id
    ).first()

    if not admin_users:
        print("\n⚠️  No admin user found!")
        print("To assign admin role to an existing user, run:")
        print("  python cmd/assign_admin_role.py <user_email>")
        print("\nExample:")
        print("  python cmd/assign_admin_role.py <EMAIL>")
    else:
        print(f"✅ Admin user exists: {admin_users.email}")


def main():
    """Main function to seed roles and permissions"""
    app = create_app()
    
    with app.app_context():
        print("🌱 Seeding roles and permissions...")
        
        # Create permissions
        print("Creating permissions...")
        created_permissions = create_default_permissions()
        if created_permissions:
            print(f"✅ Created permissions: {', '.join(created_permissions)}")
        else:
            print("ℹ️  All permissions already exist")
        
        # Create roles
        print("Creating roles...")
        created_roles = create_default_roles()
        if created_roles:
            print(f"✅ Created roles: {', '.join(created_roles)}")
        else:
            print("ℹ️  All roles already exist")
        
        # Commit permissions and roles first
        db.session.commit()
        
        # Assign permissions to roles
        print("Assigning permissions to roles...")
        assign_permissions_to_roles()
        
        # Commit role permissions
        db.session.commit()
        
        # Check for admin users
        check_admin_users()

        print("\n✅ Roles and permissions seeding completed!")
        print("\n📋 Summary:")
        print(f"   - Permissions: {len(Permission.query.all())} total")
        print(f"   - Roles: {len(Role.query.all())} total")
        print(f"   - Role-Permission mappings: {len(RolePermission.query.all())} total")


if __name__ == "__main__":
    main()
