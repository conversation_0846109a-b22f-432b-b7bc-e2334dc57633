#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to assign admin role to a user
"""

import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.helpers.extensions import db
from app.models.auth import Role
from app.models.user import User, User<PERSON><PERSON>


def assign_admin_role(email):
    """Assign admin role to user by email"""
    app = create_app()
    
    with app.app_context():
        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            print(f"❌ User with email '{email}' not found")
            return False
        
        # Find admin role
        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            print("❌ Admin role not found. Please run seed_roles_permissions.py first")
            return False
        
        # Check if user already has admin role
        existing_role = UserRole.query.filter_by(
            user_id=user.id,
            role_id=admin_role.id
        ).first()
        
        if existing_role:
            print(f"ℹ️  User '{email}' already has admin role")
            return True
        
        # Assign admin role
        user_role = UserRole(user_id=user.id, role_id=admin_role.id)
        db.session.add(user_role)
        db.session.commit()
        
        print(f"✅ Admin role assigned to user '{email}' successfully")
        return True


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python cmd/assign_admin_role.py <user_email>")
        print("Example: python cmd/assign_admin_role.py <EMAIL>")
        sys.exit(1)
    
    email = sys.argv[1]
    assign_admin_role(email)


if __name__ == "__main__":
    main()
