import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models import Role, Permission
from app.projects.permissions import init_project_roles_and_permissions

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        print("🔧 Initializing project roles and permissions...")
        init_project_roles_and_permissions()
        print("✅ Project roles and permissions initialized successfully!")
        
        # Display created roles and permissions
        print("\n📋 Created Roles:")
        roles = Role.query.filter(Role.name.like('project_%')).all()
        for role in roles:
            print(f"  - {role.name}: {role.description}")
        
        print("\n🔑 Created Permissions:")
        permissions = Permission.query.filter(Permission.name.like('%project%')).all()
        for perm in permissions:
            print(f"  - {perm.name}: {perm.description}")
        
        print("\n🎉 Initialization complete!")
