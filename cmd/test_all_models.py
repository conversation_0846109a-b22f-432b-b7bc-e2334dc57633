import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models import (
    User, Task, Project, Role, Permission, RolePermission, UserRole,
    Integration, ExternalTaskMapping, Notification, ActivityLog, Setting
)
from datetime import datetime, date

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        # Get a user for testing
        user = User.query.first()
        if not user:
            print("No users found. Please run seed.py first.")
            sys.exit(1)
            
        print(f"Using user with ID {user.id} for testing")
        
        # Test UserRole
        print("\n--- Testing UserRole ---")
        role = Role.query.first()
        if role:
            user_role = UserRole(user_id=user.id, role_id=role.id)
            db.session.add(user_role)
            db.session.commit()
            print(f"Created UserRole: {user_role}")
        else:
            print("No roles found. Skipping UserRole test.")
            
        # Test Integration
        print("\n--- Testing Integration ---")
        integration = Integration(
            user_id=user.id,
            platform="GitHub",
            access_token="sample_access_token",
            refresh_token="sample_refresh_token",
            expires_at=datetime(2026, 1, 1)
        )
        db.session.add(integration)
        db.session.commit()
        print(f"Created Integration: {integration}")
        
        # Test Project (with updated fields)
        print("\n--- Testing Project ---")
        project = Project(
            name="Test Project",
            description="A test project",
            status="Active",
            priority="High",
            start_date=date.today(),
            end_date=date(2025, 12, 31),
            created_by=user.id,
            user_id=user.id
        )
        db.session.add(project)
        db.session.commit()
        print(f"Created Project: ID={project.id}, Name={project.name}")
        
        # Test Task
        print("\n--- Testing Task ---")
        task = Task(
            title="Test Task",
            description="A test task",
            status="To Do",
            priority="Medium",
            created_by=user.id,
            project_id=project.id
        )
        db.session.add(task)
        db.session.commit()
        print(f"Created Task: ID={task.id}, Title={task.title}")
        
        # Test ExternalTaskMapping
        print("\n--- Testing ExternalTaskMapping ---")
        task_mapping = ExternalTaskMapping(
            task_id=task.id,
            integration_id=integration.id,
            external_task_id="ext-123",
            external_project_id="ext-proj-456",
            sync_mode="bidirectional"
        )
        db.session.add(task_mapping)
        db.session.commit()
        print(f"Created ExternalTaskMapping: {task_mapping}")
        
        # Test Notification
        print("\n--- Testing Notification ---")
        notification = Notification(
            user_id=user.id,
            message="This is a test notification"
        )
        db.session.add(notification)
        db.session.commit()
        print(f"Created Notification: {notification}")
        
        # Test ActivityLog
        print("\n--- Testing ActivityLog ---")
        activity_log = ActivityLog(
            user_id=user.id,
            project_id=project.id,
            task_id=task.id,
            action="CREATE",
            description="Created a new task"
        )
        db.session.add(activity_log)
        db.session.commit()
        print(f"Created ActivityLog: {activity_log}")
        
        # Test Setting
        print("\n--- Testing Setting ---")
        setting = Setting(
            user_id=user.id,
            key="theme",
            value="dark",
            is_global=False
        )
        db.session.add(setting)
        
        global_setting = Setting(
            key="application_version",
            value="1.0.0",
            is_global=True
        )
        db.session.add(global_setting)
        db.session.commit()
        print(f"Created user setting: {setting}")
        print(f"Created global setting: {global_setting}")
        
        print("\n✅ All model tests completed successfully!")
        
        # Clean up test data
        db.session.delete(setting)
        db.session.delete(global_setting)
        db.session.delete(activity_log)
        db.session.delete(notification)
        db.session.delete(task_mapping)
        db.session.delete(task)
        db.session.delete(project)
        db.session.delete(integration)
        db.session.delete(user_role)
        db.session.commit()
        print("✅ Cleaned up test data.")
