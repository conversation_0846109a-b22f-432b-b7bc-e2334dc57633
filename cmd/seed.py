import sys, os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from faker import Faker
from app import create_app
from app.helpers.extensions import db
from app.models import User, Role, Permission, RolePermission
from werkzeug.security import generate_password_hash

fake = Faker()
app = create_app()

PASSWORD = "123456Aa@"


def user_exists(email, username):
    return User.query.filter(
        (User.email == email) | (User.username == username)
    ).first()


def init_basic_roles_and_permissions():
    """Initialize basic roles and permissions"""
    try:
        # Create basic project roles
        roles_to_create = [
            {
                "name": "project_owner",
                "description": "Project owner with full permissions",
            },
            {
                "name": "project_member",
                "description": "Project member with limited permissions",
            },
            {
                "name": "project_viewer",
                "description": "Project viewer with read-only access",
            },
        ]

        for role_data in roles_to_create:
            existing_role = Role.query.filter_by(name=role_data["name"]).first()
            if not existing_role:
                role = Role(
                    name=role_data["name"],
                    description=role_data["description"],
                    created_by="system",
                )
                db.session.add(role)
                print(f"  ✅ Created role: {role_data['name']}")
            else:
                print(f"  ⚠️  Role already exists: {role_data['name']}")

        # Create basic permissions
        permissions_to_create = [
            {"name": "view_project", "description": "View project details"},
            {"name": "edit_project", "description": "Edit project settings"},
            {"name": "delete_project", "description": "Delete project"},
            {"name": "manage_project_members", "description": "Manage project members"},
            {"name": "create_task", "description": "Create new tasks"},
            {"name": "edit_task", "description": "Edit tasks"},
            {"name": "delete_task", "description": "Delete tasks"},
            {"name": "assign_task", "description": "Assign tasks to users"},
            {"name": "add_comment", "description": "Add comments"},
            {"name": "edit_comment", "description": "Edit comments"},
            {"name": "delete_comment", "description": "Delete comments"},
            {"name": "upload_attachment", "description": "Upload attachments"},
            {"name": "delete_attachment", "description": "Delete attachments"},
        ]

        for perm_data in permissions_to_create:
            existing_perm = Permission.query.filter_by(name=perm_data["name"]).first()
            if not existing_perm:
                permission = Permission(
                    name=perm_data["name"], description=perm_data["description"]
                )
                db.session.add(permission)
                print(f"  ✅ Created permission: {perm_data['name']}")
            else:
                print(f"  ⚠️  Permission already exists: {perm_data['name']}")

        db.session.commit()

        # Assign permissions to roles
        assign_permissions_to_roles()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error initializing roles: {e}")


def assign_permissions_to_roles():
    """Assign permissions to roles"""
    try:
        # Get roles
        owner_role = Role.query.filter_by(name="project_owner").first()
        member_role = Role.query.filter_by(name="project_member").first()
        viewer_role = Role.query.filter_by(name="project_viewer").first()

        # Get permissions
        all_permissions = Permission.query.all()

        # Owner gets all permissions
        if owner_role:
            for permission in all_permissions:
                existing = RolePermission.query.filter_by(
                    role_id=owner_role.id, permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=owner_role.id, permission_id=permission.id
                    )
                    db.session.add(role_perm)
            print("  ✅ Assigned all permissions to project_owner")

        # Member gets limited permissions
        if member_role:
            member_permission_names = [
                "view_project",
                "create_task",
                "edit_task",
                "assign_task",
                "add_comment",
                "edit_comment",
                "upload_attachment",
            ]
            for perm_name in member_permission_names:
                permission = Permission.query.filter_by(name=perm_name).first()
                if permission:
                    existing = RolePermission.query.filter_by(
                        role_id=member_role.id, permission_id=permission.id
                    ).first()
                    if not existing:
                        role_perm = RolePermission(
                            role_id=member_role.id, permission_id=permission.id
                        )
                        db.session.add(role_perm)
            print("  ✅ Assigned limited permissions to project_member")

        # Viewer gets only view permission
        if viewer_role:
            view_permission = Permission.query.filter_by(name="view_project").first()
            if view_permission:
                existing = RolePermission.query.filter_by(
                    role_id=viewer_role.id, permission_id=view_permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=viewer_role.id, permission_id=view_permission.id
                    )
                    db.session.add(role_perm)
            print("  ✅ Assigned view permission to project_viewer")

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error assigning permissions: {e}")


with app.app_context():
    users = []

    for _ in range(10):
        while True:
            username = fake.user_name()
            email = fake.unique.email()
            if not user_exists(email, username):
                break

        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(PASSWORD),
        )
        users.append(user)

    db.session.add_all(users)
    db.session.commit()
    print("✅ Seeded 10 fake users.")

    print("📥 Seeding roles...")
    admin_role = Role(name="admin")
    member_role = Role(name="member")
    db.session.add_all([admin_role, member_role])
    db.session.commit()
    print("✅ Roles created.")

    print("📥 Seeding permissions...")
    perm_create = Permission(name="can_create_task", description="Can create task")
    perm_edit = Permission(name="can_edit_task", description="Can edit task")
    perm_delete = Permission(name="can_delete_task", description="Can delete task")
    db.session.add_all([perm_create, perm_edit, perm_delete])
    db.session.commit()
    print("✅ Permissions created.")

    print("🔗 Mapping permissions to roles...")
    admin_perms = [
        RolePermission(role_id=admin_role.id, permission_id=perm.id)
        for perm in [perm_create, perm_edit, perm_delete]
    ]

    member_perms = [
        RolePermission(role_id=member_role.id, permission_id=perm_create.id)
    ]

    db.session.add_all(admin_perms + member_perms)
    db.session.commit()
    print("✅ Role-permission mappings done.")

    admin_user = users[0]
    admin_user.role_id = admin_role.id
    db.session.commit()
    print(f"✅ Assigned admin role to user {admin_user.username}.")

    # Initialize basic roles and permissions
    print("🔧 Initializing basic roles and permissions...")
    init_basic_roles_and_permissions()
    print("✅ Roles and permissions initialized.")

    print("📥 Seeding users...")

    print("🎉 Seed complete.")
