import sys, os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from faker import Faker
from app import create_app
from app.helpers.extensions import db
from app.models import User, Role, Permission, RolePermission, UserRole
from werkzeug.security import generate_password_hash

fake = Faker()
app = create_app()

PASSWORD = "123456Aa@"


def user_exists(email, username):
    return User.query.filter(
        (User.email == email) | (User.username == username)
    ).first()


def create_system_roles_and_permissions():
    """Create system-level roles and permissions for user management"""
    try:
        print("🔧 Creating system roles and permissions...")

        # System roles
        system_roles = [
            {
                'name': 'admin',
                'description': 'System administrator with full access',
                'created_by': 'system'
            },
            {
                'name': 'member',
                'description': 'Regular user with basic permissions',
                'created_by': 'system'
            }
        ]

        created_roles = []
        for role_data in system_roles:
            existing = Role.query.filter_by(name=role_data['name']).first()
            if not existing:
                role = Role(**role_data)
                db.session.add(role)
                created_roles.append(role_data['name'])
                print(f"  ✅ Created system role: {role_data['name']}")
            else:
                print(f"  ⚠️  System role already exists: {role_data['name']}")

        # System permissions
        system_permissions = [
            # User management permissions
            {
                'name': 'manage_users',
                'description': 'Create, update, delete users',
                'resource': 'user',
                'action': 'manage'
            },
            {
                'name': 'view_users',
                'description': 'View user profiles and lists',
                'resource': 'user',
                'action': 'read'
            },
            {
                'name': 'update_own_profile',
                'description': 'Update own user profile',
                'resource': 'user',
                'action': 'update_own'
            },

            # Role and permission management
            {
                'name': 'manage_roles',
                'description': 'Create, update, delete roles',
                'resource': 'role',
                'action': 'manage'
            },
            {
                'name': 'assign_roles',
                'description': 'Assign roles to users',
                'resource': 'role',
                'action': 'assign'
            },

            # System permissions
            {
                'name': 'view_statistics',
                'description': 'View system statistics',
                'resource': 'system',
                'action': 'view_stats'
            },
            {
                'name': 'manage_system',
                'description': 'Manage system settings',
                'resource': 'system',
                'action': 'manage'
            }
        ]

        created_permissions = []
        for perm_data in system_permissions:
            existing = Permission.query.filter_by(name=perm_data['name']).first()
            if not existing:
                permission = Permission(**perm_data)
                db.session.add(permission)
                created_permissions.append(perm_data['name'])
                print(f"  ✅ Created system permission: {perm_data['name']}")
            else:
                print(f"  ⚠️  System permission already exists: {perm_data['name']}")

        db.session.commit()

        # Assign permissions to system roles
        assign_system_permissions_to_roles()

        return created_roles, created_permissions

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error creating system roles: {e}")
        return [], []


def assign_system_permissions_to_roles():
    """Assign system permissions to system roles"""
    try:
        # Admin gets all system permissions
        admin_role = Role.query.filter_by(name='admin').first()
        if admin_role:
            system_permissions = Permission.query.filter(
                Permission.resource.in_(['user', 'role', 'system'])
            ).all()

            for permission in system_permissions:
                existing = RolePermission.query.filter_by(
                    role_id=admin_role.id,
                    permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=admin_role.id,
                        permission_id=permission.id
                    )
                    db.session.add(role_perm)
            print("  ✅ Assigned all system permissions to admin")

        # Member gets limited permissions
        member_role = Role.query.filter_by(name='member').first()
        if member_role:
            member_permissions = ['update_own_profile']

            for perm_name in member_permissions:
                permission = Permission.query.filter_by(name=perm_name).first()
                if permission:
                    existing = RolePermission.query.filter_by(
                        role_id=member_role.id,
                        permission_id=permission.id
                    ).first()
                    if not existing:
                        role_perm = RolePermission(
                            role_id=member_role.id,
                            permission_id=permission.id
                        )
                        db.session.add(role_perm)
            print("  ✅ Assigned basic permissions to member")

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error assigning system permissions: {e}")


def init_basic_roles_and_permissions():
    """Initialize basic project roles and permissions"""
    try:
        print("🔧 Creating project roles and permissions...")

        # Create basic project roles
        roles_to_create = [
            {
                "name": "project_owner",
                "description": "Project owner with full permissions",
            },
            {
                "name": "project_member",
                "description": "Project member with limited permissions",
            },
            {
                "name": "project_viewer",
                "description": "Project viewer with read-only access",
            },
        ]

        for role_data in roles_to_create:
            existing_role = Role.query.filter_by(name=role_data["name"]).first()
            if not existing_role:
                role = Role(
                    name=role_data["name"],
                    description=role_data["description"],
                    created_by="system",
                )
                db.session.add(role)
                print(f"  ✅ Created project role: {role_data['name']}")
            else:
                print(f"  ⚠️  Project role already exists: {role_data['name']}")

        # Create basic project permissions
        permissions_to_create = [
            {"name": "view_project", "description": "View project details", "resource": "project", "action": "read"},
            {"name": "edit_project", "description": "Edit project settings", "resource": "project", "action": "update"},
            {"name": "delete_project", "description": "Delete project", "resource": "project", "action": "delete"},
            {"name": "manage_project_members", "description": "Manage project members", "resource": "project", "action": "manage_members"},
            {"name": "create_task", "description": "Create new tasks", "resource": "task", "action": "create"},
            {"name": "edit_task", "description": "Edit tasks", "resource": "task", "action": "update"},
            {"name": "delete_task", "description": "Delete tasks", "resource": "task", "action": "delete"},
            {"name": "assign_task", "description": "Assign tasks to users", "resource": "task", "action": "assign"},
            {"name": "add_comment", "description": "Add comments", "resource": "comment", "action": "create"},
            {"name": "edit_comment", "description": "Edit comments", "resource": "comment", "action": "update"},
            {"name": "delete_comment", "description": "Delete comments", "resource": "comment", "action": "delete"},
            {"name": "upload_attachment", "description": "Upload attachments", "resource": "attachment", "action": "create"},
            {"name": "delete_attachment", "description": "Delete attachments", "resource": "attachment", "action": "delete"},
        ]

        for perm_data in permissions_to_create:
            existing_perm = Permission.query.filter_by(name=perm_data["name"]).first()
            if not existing_perm:
                permission = Permission(**perm_data)
                db.session.add(permission)
                print(f"  ✅ Created project permission: {perm_data['name']}")
            else:
                print(f"  ⚠️  Project permission already exists: {perm_data['name']}")

        db.session.commit()

        # Assign permissions to project roles
        assign_permissions_to_roles()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error initializing project roles: {e}")


def assign_permissions_to_roles():
    """Assign project permissions to project roles"""
    try:
        # Get project roles
        owner_role = Role.query.filter_by(name="project_owner").first()
        member_role = Role.query.filter_by(name="project_member").first()
        viewer_role = Role.query.filter_by(name="project_viewer").first()

        # Get project permissions (exclude system permissions)
        project_permissions = Permission.query.filter(
            Permission.resource.in_(['project', 'task', 'comment', 'attachment'])
        ).all()

        # Owner gets all project permissions
        if owner_role:
            for permission in project_permissions:
                existing = RolePermission.query.filter_by(
                    role_id=owner_role.id, permission_id=permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=owner_role.id, permission_id=permission.id
                    )
                    db.session.add(role_perm)
            print("  ✅ Assigned all project permissions to project_owner")

        # Member gets limited project permissions
        if member_role:
            member_permission_names = [
                "view_project",
                "create_task",
                "edit_task",
                "assign_task",
                "add_comment",
                "edit_comment",
                "upload_attachment",
            ]
            for perm_name in member_permission_names:
                permission = Permission.query.filter_by(name=perm_name).first()
                if permission:
                    existing = RolePermission.query.filter_by(
                        role_id=member_role.id, permission_id=permission.id
                    ).first()
                    if not existing:
                        role_perm = RolePermission(
                            role_id=member_role.id, permission_id=permission.id
                        )
                        db.session.add(role_perm)
            print("  ✅ Assigned limited permissions to project_member")

        # Viewer gets only view permission
        if viewer_role:
            view_permission = Permission.query.filter_by(name="view_project").first()
            if view_permission:
                existing = RolePermission.query.filter_by(
                    role_id=viewer_role.id, permission_id=view_permission.id
                ).first()
                if not existing:
                    role_perm = RolePermission(
                        role_id=viewer_role.id, permission_id=view_permission.id
                    )
                    db.session.add(role_perm)
            print("  ✅ Assigned view permission to project_viewer")

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error assigning project permissions: {e}")


def create_test_users():
    """Create test users for development"""
    try:
        print("👥 Creating test users...")

        # Test admin user
        admin_email = "<EMAIL>"
        admin_user = User.query.filter_by(email=admin_email).first()

        if not admin_user:
            admin_user = User(
                username="admin",
                email=admin_email,
                password_hash=generate_password_hash("Admin123!"),
                auth_provider='local'
            )
            db.session.add(admin_user)
            db.session.flush()

            # Assign admin role
            admin_role = Role.query.filter_by(name='admin').first()
            if admin_role:
                user_role = UserRole(user_id=admin_user.id, role_id=admin_role.id)
                db.session.add(user_role)

            print(f"  ✅ Created admin user: {admin_email} (password: Admin123!)")
        else:
            print(f"  ⚠️  Admin user already exists: {admin_email}")

        # Test member user
        member_email = "<EMAIL>"
        member_user = User.query.filter_by(email=member_email).first()

        if not member_user:
            member_user = User(
                username="member",
                email=member_email,
                password_hash=generate_password_hash("Member123!"),
                auth_provider='local'
            )
            db.session.add(member_user)
            db.session.flush()

            # Assign member role
            member_role = Role.query.filter_by(name='member').first()
            if member_role:
                user_role = UserRole(user_id=member_user.id, role_id=member_role.id)
                db.session.add(user_role)

            print(f"  ✅ Created member user: {member_email} (password: Member123!)")
        else:
            print(f"  ⚠️  Member user already exists: {member_email}")

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error creating test users: {e}")


def assign_roles_to_existing_users():
    """Assign default member role to existing users without roles"""
    try:
        print("🔗 Assigning roles to existing users...")

        member_role = Role.query.filter_by(name='member').first()
        if not member_role:
            print("  ⚠️  Member role not found, skipping role assignment")
            return

        # Find users without any roles
        users_without_roles = db.session.query(User).outerjoin(UserRole).filter(
            UserRole.user_id.is_(None)
        ).all()

        assigned_count = 0
        for user in users_without_roles:
            user_role = UserRole(user_id=user.id, role_id=member_role.id)
            db.session.add(user_role)
            assigned_count += 1

        if assigned_count > 0:
            db.session.commit()
            print(f"  ✅ Assigned member role to {assigned_count} existing users")
        else:
            print("  ℹ️  All existing users already have roles")

    except Exception as e:
        db.session.rollback()
        print(f"  ❌ Error assigning roles to existing users: {e}")


def assign_admin_role_to_user(email):
    """Assign admin role to user by email - utility function"""
    try:
        user = User.query.filter_by(email=email).first()
        if not user:
            print(f"❌ User with email '{email}' not found")
            return False

        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            print("❌ Admin role not found. Please run seeding first")
            return False

        existing_role = UserRole.query.filter_by(
            user_id=user.id,
            role_id=admin_role.id
        ).first()

        if existing_role:
            print(f"ℹ️  User '{email}' already has admin role")
            return True

        user_role = UserRole(user_id=user.id, role_id=admin_role.id)
        db.session.add(user_role)
        db.session.commit()

        print(f"✅ Admin role assigned to user '{email}' successfully")
        return True

    except Exception as e:
        db.session.rollback()
        print(f"❌ Error assigning admin role: {e}")
        return False


def main():
    """Main seeding function"""
    print("🌱 Starting comprehensive seeding...")
    print("=" * 50)

    # Step 1: Create system roles and permissions
    create_system_roles_and_permissions()

    # Step 2: Create project roles and permissions
    init_basic_roles_and_permissions()

    # Step 3: Create test users (admin and member)
    create_test_users()

    # Step 4: Create fake users for development
    print("👥 Creating fake users...")
    users = []

    for _ in range(10):
        while True:
            username = fake.user_name()
            email = fake.unique.email()
            if not user_exists(email, username):
                break

        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(PASSWORD),
            auth_provider='local'
        )
        users.append(user)

    db.session.add_all(users)
    db.session.commit()

    # Assign member role to fake users
    member_role = Role.query.filter_by(name='member').first()
    if member_role:
        for user in users:
            user_role = UserRole(user_id=user.id, role_id=member_role.id)
            db.session.add(user_role)
        db.session.commit()

    print(f"  ✅ Created {len(users)} fake users with member role")

    # Step 5: Assign roles to any existing users without roles
    assign_roles_to_existing_users()

    # Step 6: Print summary
    print("\n📊 Seeding Summary:")
    print("=" * 30)
    print(f"  Roles: {Role.query.count()} total")
    print(f"  Permissions: {Permission.query.count()} total")
    print(f"  Role-Permission mappings: {RolePermission.query.count()} total")
    print(f"  Users: {User.query.count()} total")
    print(f"  User-Role mappings: {UserRole.query.count()} total")

    # Check admin users
    admin_role = Role.query.filter_by(name='admin').first()
    if admin_role:
        admin_users = db.session.query(User).join(UserRole).filter(
            UserRole.role_id == admin_role.id
        ).all()
        print(f"  Admin users: {len(admin_users)} ({', '.join([u.email for u in admin_users])})")

    print("\n� Test Accounts:")
    print("  Admin: <EMAIL> / Admin123!")
    print("  Member: <EMAIL> / Member123!")

    print("\n🛠️  Management Commands:")
    print("  To assign admin role to existing user:")
    print("    python cmd/seed.py --assign-admin <email>")

    print("\n🎉 Comprehensive seeding completed successfully!")


if __name__ == "__main__":
    import sys

    # Check for command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--assign-admin" and len(sys.argv) > 2:
            # Assign admin role to specific user
            with app.app_context():
                assign_admin_role_to_user(sys.argv[2])
        elif sys.argv[1] == "--help":
            print("Usage:")
            print("  python cmd/seed.py                    # Run full seeding")
            print("  python cmd/seed.py --assign-admin <email>  # Assign admin role to user")
            print("  python cmd/seed.py --help             # Show this help")
        else:
            print("Unknown command. Use --help for usage information.")
    else:
        # Run full seeding
        with app.app_context():
            main()
