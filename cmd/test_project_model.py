import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models import Project, User
from datetime import datetime, date

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        # Get a valid user_id for testing
        user = User.query.first()
        if not user:
            print("No users found in the database. Please run the seed script first.")
            sys.exit(1)
        
        print(f"Using user ID: {user.id} for test")
        
        # Create a test project with all fields
        test_project = Project(
            name="Test Project",
            description="This is a test project to verify the updated model",
            status="Active",
            priority="High",
            start_date=date.today(),
            end_date=date(2025, 12, 31),
            created_by=user.id,
            user_id=user.id
        )
        
        try:
            # Add and commit to the database
            db.session.add(test_project)
            db.session.commit()
            print(f"✅ Successfully created test project with ID: {test_project.id}")
            
            # Retrieve the project to verify all fields
            project = Project.query.get(test_project.id)
            print("\nProject details:")
            print(f"ID: {project.id}")
            print(f"Name: {project.name}")
            print(f"Description: {project.description}")
            print(f"Status: {project.status}")
            print(f"Priority: {project.priority}")
            print(f"Start date: {project.start_date}")
            print(f"End date: {project.end_date}")
            print(f"Created by: {project.created_by}")
            print(f"User ID: {project.user_id}")
            print(f"Created at: {project.created_at}")
            print(f"Updated at: {project.updated_at}")
            print(f"Deleted at: {project.deleted_at}")
            
            # Clean up
            db.session.delete(project)
            db.session.commit()
            print("\n✅ Test cleanup completed.")
            
        except Exception as e:
            print(f"❌ Error during test: {str(e)}")
            db.session.rollback()
