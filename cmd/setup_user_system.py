#!/usr/bin/env python3
"""
Complete setup script for user management system
"""

import sys
import os
import subprocess

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.helpers.extensions import db
from app.models.auth import Role, Permission, RolePermission
from app.models.user import User, User<PERSON><PERSON>


def run_migrations():
    """Run database migrations"""
    print("🔄 Running database migrations...")
    try:
        result = subprocess.run([
            'docker-compose', 'run', '--rm', 'flask_app', 
            'flask', 'db', 'upgrade'
        ], capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        if result.returncode == 0:
            print("✅ Database migrations completed successfully")
            return True
        else:
            print(f"❌ Migration failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error running migrations: {str(e)}")
        return False


def seed_roles_and_permissions():
    """Seed roles and permissions"""
    print("🌱 Seeding roles and permissions...")
    try:
        # Import and run the seeding script
        from seed_roles_permissions import main as seed_main
        seed_main()
        return True
    except Exception as e:
        print(f"❌ Error seeding roles and permissions: {str(e)}")
        return False


def create_test_users():
    """Create test users for development"""
    app = create_app()
    
    with app.app_context():
        print("👥 Creating test users...")
        
        # Test admin user
        admin_email = "<EMAIL>"
        admin_user = User.query.filter_by(email=admin_email).first()
        
        if not admin_user:
            from werkzeug.security import generate_password_hash
            
            admin_user = User(
                username="admin",
                email=admin_email,
                password_hash=generate_password_hash("Admin123!"),
                auth_provider='local'
            )
            db.session.add(admin_user)
            db.session.flush()
            
            # Assign admin role
            admin_role = Role.query.filter_by(name='admin').first()
            if admin_role:
                user_role = UserRole(user_id=admin_user.id, role_id=admin_role.id)
                db.session.add(user_role)
            
            print(f"✅ Created admin user: {admin_email} (password: Admin123!)")
        else:
            print(f"ℹ️  Admin user already exists: {admin_email}")
        
        # Test member user
        member_email = "<EMAIL>"
        member_user = User.query.filter_by(email=member_email).first()
        
        if not member_user:
            from werkzeug.security import generate_password_hash
            
            member_user = User(
                username="member",
                email=member_email,
                password_hash=generate_password_hash("Member123!"),
                auth_provider='local'
            )
            db.session.add(member_user)
            db.session.flush()
            
            # Assign member role
            member_role = Role.query.filter_by(name='member').first()
            if member_role:
                user_role = UserRole(user_id=member_user.id, role_id=member_role.id)
                db.session.add(user_role)
            
            print(f"✅ Created member user: {member_email} (password: Member123!)")
        else:
            print(f"ℹ️  Member user already exists: {member_email}")
        
        db.session.commit()


def verify_setup():
    """Verify the setup is working correctly"""
    app = create_app()
    
    with app.app_context():
        print("\n🔍 Verifying setup...")
        
        # Check roles
        roles = Role.query.all()
        print(f"✅ Roles: {len(roles)} ({', '.join([r.name for r in roles])})")
        
        # Check permissions
        permissions = Permission.query.all()
        print(f"✅ Permissions: {len(permissions)} total")
        
        # Check role-permission mappings
        role_perms = RolePermission.query.all()
        print(f"✅ Role-Permission mappings: {len(role_perms)} total")
        
        # Check users
        users = User.query.all()
        print(f"✅ Users: {len(users)} total")
        
        # Check admin users
        admin_role = Role.query.filter_by(name='admin').first()
        if admin_role:
            admin_users = db.session.query(User).join(UserRole).filter(
                UserRole.role_id == admin_role.id
            ).all()
            print(f"✅ Admin users: {len(admin_users)} ({', '.join([u.email for u in admin_users])})")
        
        print("\n🎉 User management system setup completed successfully!")
        
        return True


def print_usage_info():
    """Print usage information"""
    print("\n📖 Usage Information:")
    print("=" * 50)
    print("\n🔐 Test Accounts:")
    print("  Admin: <EMAIL> / Admin123!")
    print("  Member: <EMAIL> / Member123!")
    
    print("\n🛠️  Management Commands:")
    print("  Assign admin role: python cmd/assign_admin_role.py <email>")
    print("  Seed roles/permissions: python cmd/seed_roles_permissions.py")
    
    print("\n🌐 API Endpoints:")
    print("  POST /auth/register - Register new user")
    print("  POST /auth/login - Login user")
    print("  POST /auth/logout - Logout user")
    print("  GET /auth/me - Get current user info")
    print("  GET /users/profile - Get current user profile")
    print("  PUT /users/profile - Update current user profile")
    print("  GET /users - List all users (admin only)")
    print("  GET /users/<id> - Get user by ID")
    print("  POST /users/<id>/roles - Assign role to user (admin only)")
    print("  DELETE /users/<id>/roles/<role> - Remove role from user (admin only)")
    
    print("\n🔑 JWT Token includes:")
    print("  - User ID")
    print("  - User roles")
    print("  - is_admin flag")


def main():
    """Main setup function"""
    print("🚀 Setting up User Management System")
    print("=" * 50)
    
    # Step 1: Run migrations
    if not run_migrations():
        print("❌ Setup failed at migration step")
        return False
    
    # Step 2: Seed roles and permissions
    if not seed_roles_and_permissions():
        print("❌ Setup failed at seeding step")
        return False
    
    # Step 3: Create test users (optional, for development)
    if len(sys.argv) > 1 and sys.argv[1] == "--with-test-users":
        create_test_users()
    
    # Step 4: Verify setup
    if not verify_setup():
        print("❌ Setup verification failed")
        return False
    
    # Step 5: Print usage info
    print_usage_info()
    
    return True


if __name__ == "__main__":
    if "--help" in sys.argv or "-h" in sys.argv:
        print("User Management System Setup")
        print("Usage: python cmd/setup_user_system.py [--with-test-users]")
        print("")
        print("Options:")
        print("  --with-test-users    Create test admin and member users")
        print("  --help, -h          Show this help message")
        sys.exit(0)
    
    success = main()
    sys.exit(0 if success else 1)
