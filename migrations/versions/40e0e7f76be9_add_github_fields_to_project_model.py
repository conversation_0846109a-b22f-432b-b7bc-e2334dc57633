"""Add GitHub fields to Project model

Revision ID: 40e0e7f76be9
Revises: 88d061fb3e64
Create Date: 2025-06-11 16:18:41.230458

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '40e0e7f76be9'
down_revision = '88d061fb3e64'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_projects',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('project_id', sa.Integer(), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'project_id', name='unique_user_project')
    )
    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.add_column(sa.Column('github_repo_id', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('github_repo_name', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('github_repo_url', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('github_repo_full_name', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('is_github_synced', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('last_github_sync', sa.DateTime(), nullable=True))
        batch_op.alter_column('priority',
               existing_type=mysql.VARCHAR(length=50),
               type_=sa.String(length=20),
               existing_nullable=True)
        batch_op.alter_column('user_id',
               existing_type=mysql.INTEGER(),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.alter_column('user_id',
               existing_type=mysql.INTEGER(),
               nullable=False)
        batch_op.alter_column('priority',
               existing_type=sa.String(length=20),
               type_=mysql.VARCHAR(length=50),
               existing_nullable=True)
        batch_op.drop_column('last_github_sync')
        batch_op.drop_column('is_github_synced')
        batch_op.drop_column('github_repo_full_name')
        batch_op.drop_column('github_repo_url')
        batch_op.drop_column('github_repo_name')
        batch_op.drop_column('github_repo_id')

    op.drop_table('user_projects')
    # ### end Alembic commands ###
