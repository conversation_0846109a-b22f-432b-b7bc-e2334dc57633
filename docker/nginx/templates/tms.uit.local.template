server {
    listen 80;
    listen [::]:80;

    server_name ${NGINX_HOST};
    root /app;
    index index.php index.html index.htm;

    set $upstream_flask flask_app:5000;

    access_log /var/log/nginx/tms.uit.local-access.log;
    error_log /var/log/nginx/tms.uit.local-error.log debug;

    location / {
        proxy_pass http://$upstream_flask;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";

        # Add additional proxy headers for better reliability
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Port $server_port;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
