import os

from app import create_app

app = create_app()

if __name__ == "__main__":

    dns = f"http://{os.getenv('NGINX_HOST', 'localhost')}:{os.getenv('NGINX_HOST_HTTP_PORT', '80')}"
    print()
    print("=======================================================================")
    print(
        "App is running at: ",
        dns,
    )
    print(
        "App docs is running at: ",
        dns + "/apidocs",
    )
    print("=======================================================================")
    print()
    app.run(
        host=os.getenv("FLASK_HOST"),
        port=int(os.getenv("FLASK_PORT", "5000")),
        debug=os.getenv("DEBUG", "False"),
    )
