# GitHub Sync Feature

Tính năng đồng bộ GitHub cho phép người dùng đồng bộ repositories và issues từ GitHub về hệ thống TMS local.

## Tổng quan

Tính năng này được chia thành hai phần chính:
1. **Repository Sync** (trong `app/projects/`) - Đồng bộ GitHub repositories thành local projects
2. **Issues Sync** (trong `app/tasks/`) - <PERSON><PERSON>ng bộ GitHub issues thành local tasks

## Yêu cầu

- User phải có GitHub OAuth integration được kích hoạt
- Access token GitHub hợp lệ với quyền `user:email` và `repo`

## API Endpoints

### 1. Fetch GitHub Repositories

**GET** `/projects/github/repositories`

L<PERSON>y danh sách tất cả repositories của user từ GitHub.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "All repositories fetched successfully",
  "data": {
    "repositories": [
      {
        "id": "123456",
        "name": "my-repo",
        "full_name": "username/my-repo",
        "description": "My awesome repository",
        "html_url": "https://github.com/username/my-repo",
        "private": false,
        "language": "Python",
        "stargazers_count": 10,
        "open_issues_count": 5
      }
    ],
    "total_count": 1
  }
}
```

### 2. Sync Repositories to Projects

**POST** `/projects/github/sync`

Đồng bộ các repositories được chọn thành local projects.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Body:**
```json
{
  "repository_urls": [
    "https://github.com/username/repo1",
    "https://github.com/username/repo2"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Repositories synced successfully",
  "data": {
    "synced_projects": [
      {
        "action": "created",
        "project": {
          "id": 1,
          "name": "repo1",
          "description": "Repository description",
          "github_repo_id": "123456",
          "github_repo_url": "https://github.com/username/repo1",
          "is_github_synced": true
        }
      }
    ],
    "total_synced": 1,
    "errors": []
  }
}
```

### 3. Sync GitHub Issues to Tasks

**POST** `/tasks/github/sync/<project_id>`

Đồng bộ tất cả issues từ GitHub repository thành local tasks.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Issues synced successfully",
  "data": {
    "synced_tasks": [
      {
        "action": "created",
        "task": {
          "id": 1,
          "title": "Fix bug in authentication",
          "description": "Issue description from GitHub",
          "status": "To Do",
          "github_issue_id": "789012",
          "github_issue_number": 1,
          "github_issue_url": "https://github.com/username/repo/issues/1",
          "is_github_synced": true
        }
      }
    ],
    "total_synced": 1,
    "total_issues": 1,
    "errors": []
  }
}
```

### 4. Complete Project Sync

**POST** `/projects/<project_id>/github/sync-complete`

Thực hiện đồng bộ hoàn chỉnh: cập nhật thông tin project và đồng bộ tất cả issues.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Project synced completely",
  "data": {
    "project_updated": true,
    "synced_tasks": [...],
    "total_synced": 5,
    "total_issues": 5,
    "errors": [],
    "project": {...}
  }
}
```

## Workflow Sử Dụng

### 1. Lấy danh sách repositories
```bash
curl -X GET "http://localhost:5001/projects/github/repositories" \
  -H "Authorization: Bearer <jwt_token>"
```

### 2. Chọn và sync repositories
```bash
curl -X POST "http://localhost:5001/projects/github/sync" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "repository_urls": [
      "https://github.com/username/my-awesome-project"
    ]
  }'
```

### 3. Sync issues cho project
```bash
curl -X POST "http://localhost:5001/tasks/github/sync/1" \
  -H "Authorization: Bearer <jwt_token>"
```

### 4. Hoặc sync hoàn chỉnh
```bash
curl -X POST "http://localhost:5001/projects/1/github/sync-complete" \
  -H "Authorization: Bearer <jwt_token>"
```

## Mapping Rules

### Repository → Project
- `name` → `name`
- `description` → `description`
- `html_url` → `github_repo_url`
- `full_name` → `github_repo_full_name`
- `id` → `github_repo_id`
- `is_github_synced` = `true`

### Issue → Task
- `title` → `title`
- `body` → `description`
- `state` → `status` (open → "To Do", closed → "Done")
- `milestone.due_on` → `due_date`
- `id` → `github_issue_id`
- `number` → `github_issue_number`
- `html_url` → `github_issue_url`
- `is_github_synced` = `true`

## Error Handling

- **400 Bad Request**: Invalid input hoặc không có GitHub integration
- **404 Not Found**: Project không tồn tại hoặc không phải GitHub project
- **500 Internal Server Error**: Lỗi server hoặc GitHub API

## Database Changes

### Project Model
Thêm các fields:
- `github_repo_id`: GitHub repository ID
- `github_repo_name`: Repository name
- `github_repo_url`: Repository URL
- `github_repo_full_name`: Full name (owner/repo)
- `is_github_synced`: Boolean flag
- `last_github_sync`: Timestamp của lần sync cuối

### Task Model
Thêm các fields:
- `github_issue_id`: GitHub issue ID
- `github_issue_number`: Issue number
- `github_issue_url`: Issue URL
- `is_github_synced`: Boolean flag
- `last_github_sync`: Timestamp của lần sync cuối

## Testing

Chạy tests:
```bash
python tests/test_github_sync.py
```

## Notes

- Tính năng này yêu cầu GitHub OAuth integration đã được thiết lập
- Pull requests sẽ bị bỏ qua khi sync issues
- Sync là one-way từ GitHub về local, không sync ngược lại
- Các items đã sync sẽ được update nếu sync lại
