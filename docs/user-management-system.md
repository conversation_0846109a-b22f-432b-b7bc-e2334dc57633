# User Management System

Hệ thống quản lý người dùng với phân quyền theo cấp bậc.

## API Endpoints

### Authentication
```
POST /auth/register          # Đăng ký
POST /auth/login             # Đăng nhập
POST /auth/logout            # Đăng xuất
GET  /auth/me                # Thông tin user hiện tại
PUT  /auth/update-profile    # Cập nhật profile
POST /auth/forgot-password   # Quên mật khẩu
POST /auth/reset-password    # Reset mật khẩu
POST /auth/change-password   # Đ<PERSON>i mật khẩu
```

### User Management
```
GET  /users                  # Danh sách users (admin, project_owner, project_manager)
GET  /users/{id}/roles       # Roles của user (admin, project_owner, project_manager)
GET  /users/{id}/permissions # Permissions của user (admin, project_owner, project_manager)
POST /users/{id}/roles       # Assign role (admin, project_owner, project_manager with restrictions)
DELETE /users/{id}/roles/{role} # Remove role (admin, project_owner, project_manager with restrictions)
```

## Update Profile Example

### Update profile với PUT /auth/update-profile
```bash
curl -X PUT http://localhost:8084/auth/update-profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "new_username",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "bio": "Updated bio information"
  }'
```

### Response
```json
{
  "success": true,
  "message": "Profile updated successfully. Updated fields: username, email",
  "data": {
    "id": 1,
    "username": "new_username",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "bio": "Updated bio information",
    "roles": [
      {
        "id": 1,
        "name": "admin",
        "description": "System administrator"
      }
    ],
    "role_names": ["admin"],
    "is_admin": true,
    "auth_provider": "local",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T11:00:00Z"
  },
  "code": 200
}
```

## Test Accounts
- **Admin**: <EMAIL> / Admin123!
- **Member**: <EMAIL> / Member123!
- **Project Owner**: <EMAIL> / ProjectOwner123!
- **Project Manager**: <EMAIL> / ProjectManager123!

## Setup Commands
```bash
# 1. Run migrations
docker-compose run --rm flask_app flask db upgrade

# 2. Seed everything
python cmd/seed.py
```

## Key Changes

### Refactored update_password → update_profile
- **Old**: `POST /auth/update-password` (duplicate of change-password)
- **New**: `PUT /auth/update-profile` (comprehensive profile update)

### Features:
- ✅ Update username, email, phone, bio
- ✅ Email validation and uniqueness check
- ✅ Partial updates (only send fields you want to change)
- ✅ Returns updated user data with roles
- ✅ Detailed success message showing what was updated

### Validation:
- Email format validation
- Email uniqueness across all users
- Only allowed fields can be updated
- Preserves existing data for fields not provided

## Swagger Documentation
Truy cập: `http://tms.uit.local:8084/apidocs/`

Test endpoint `/auth/update-profile` với JWT token để cập nhật profile.
