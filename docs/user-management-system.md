# User Management System

Hệ thống quản lý người dùng toàn diện với đăng ký, đ<PERSON><PERSON> nh<PERSON>, phân quyền và quản lý profile.

## Tính năng

### 🔐 Authentication
- Đăng ký người dùng mới
- Đăng nhập/đăng xuất
- JWT authentication với roles
- GitHub OAuth integration
- Password reset functionality
- Change password

### 👥 User Management
- Xem và cập nhật profile
- Quản lý danh sách users (admin only)
- <PERSON><PERSON> quyền người dùng
- Role-based access control

### 🛡️ Authorization
- System roles: admin, member
- Project roles: project_owner, project_member, project_viewer
- Granular permissions
- Permission-based route protection

## Setup

### 1. Chạy Migration
```bash
docker-compose run --rm flask_app flask db upgrade
```

### 2. Seed Data
```bash
# Seed toàn bộ hệ thống (roles, permissions, test users)
python cmd/seed.py

# Assign admin role cho user cụ thể
python cmd/seed.py --assign-admin <EMAIL>

# Xem help
python cmd/seed.py --help
```

### 3. Test Accounts
Sau khi seed, bạn có thể sử dụng các tài khoản test:
- **Admin**: <EMAIL> / Admin123!
- **Member**: <EMAIL> / Member123!

## API Endpoints

### Authentication
```
POST /auth/register          # Đăng ký
POST /auth/login             # Đăng nhập
POST /auth/logout            # Đăng xuất
GET  /auth/me                # Thông tin user hiện tại
POST /auth/forgot-password   # Quên mật khẩu
POST /auth/reset-password    # Reset mật khẩu
POST /auth/change-password   # Đổi mật khẩu
```

### User Management
```
GET  /users/profile          # Profile hiện tại
PUT  /users/profile          # Cập nhật profile
GET  /users                  # Danh sách users (admin only)
GET  /users/{id}             # User theo ID
GET  /users/{id}/roles       # Roles của user
GET  /users/{id}/permissions # Permissions của user
POST /users/{id}/roles       # Assign role (admin only)
DELETE /users/{id}/roles/{role} # Remove role (admin only)
```

## Roles & Permissions

### System Roles

#### Admin
- Quản lý toàn bộ hệ thống
- Quản lý users và roles
- Xem statistics
- Tất cả permissions

#### Member
- User thông thường
- Cập nhật profile cá nhân
- Tham gia projects

### Project Roles

#### Project Owner
- Tạo và quản lý project
- Quản lý members
- Tất cả permissions trong project

#### Project Member
- Tham gia project
- Tạo và edit tasks
- Add comments

#### Project Viewer
- Chỉ xem project
- Read-only access

## JWT Token Structure

JWT token bao gồm:
```json
{
  "sub": "user_id",
  "roles": ["admin", "member"],
  "is_admin": true,
  "iat": 1234567890,
  "exp": 1234567890
}
```

## Usage Examples

### 1. Đăng ký user mới
```bash
curl -X POST http://localhost:8084/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "Password123!"
  }'
```

### 2. Đăng nhập
```bash
curl -X POST http://localhost:8084/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'
```

### 3. Xem profile (cần JWT token)
```bash
curl -X GET http://localhost:8084/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Cập nhật profile
```bash
curl -X PUT http://localhost:8084/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "updated_username",
    "bio": "Updated bio"
  }'
```

### 5. Assign admin role (admin only)
```bash
curl -X POST http://localhost:8084/users/123/roles \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role_name": "admin"
  }'
```

## Permission Decorators

### Sử dụng trong code
```python
from app.helpers.permissions import admin_required, role_required, permission_required

@admin_required
def admin_only_function():
    pass

@role_required('member')
def member_function():
    pass

@permission_required('manage_users')
def manage_users_function():
    pass
```

## Database Schema

### Core Tables
- `user` - Thông tin người dùng
- `roles` - Định nghĩa roles
- `permissions` - Định nghĩa permissions
- `user_roles` - Mapping user-role
- `role_permissions` - Mapping role-permission

### Relationships
```
User ←→ UserRole ←→ Role ←→ RolePermission ←→ Permission
```

## Security Features

- Password hashing với Werkzeug
- JWT token với expiration
- Token blacklist cho logout
- CSRF protection cho OAuth
- Input validation
- SQL injection protection

## Development

### Thêm permission mới
1. Thêm vào `cmd/seed.py` trong `create_system_roles_and_permissions()`
2. Chạy lại seeding
3. Sử dụng decorator `@permission_required('new_permission')`

### Thêm role mới
1. Thêm vào `cmd/seed.py`
2. Định nghĩa permissions cho role
3. Chạy lại seeding

## Troubleshooting

### Lỗi "Admin role not found"
```bash
python cmd/seed.py  # Chạy lại seeding
```

### Lỗi "Permission denied"
Kiểm tra user có đúng role/permission không:
```bash
curl -X GET http://localhost:8084/users/{id}/roles \
  -H "Authorization: Bearer JWT_TOKEN"
```

### Reset toàn bộ roles/permissions
```bash
# Xóa data cũ và seed lại
docker-compose run --rm flask_app flask db downgrade
docker-compose run --rm flask_app flask db upgrade
python cmd/seed.py
```
