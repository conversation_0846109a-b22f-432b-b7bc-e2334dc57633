# GitHub OAuth Setup Guide

## Overview

The TMS Python Backend includes GitHub OAuth integration that allows users to:
- Sign in with their GitHub account
- Link their existing account to GitHub
- Unlink their GitHub account
- Access GitHub user information and repositories

## Prerequisites

Before setting up GitHub OAuth, ensure you have:
- A GitHub account
- Admin access to create GitHub OAuth Apps
- Docker and <PERSON>er Compose installed
- Basic understanding of OAuth 2.0 flow

## 1. GitHub OAuth App Configuration

### Step 1: Create a GitHub OAuth App

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click **"New OAuth App"**
3. Fill in the application details:
   - **Application name**: `TMS Python Backend` (or your preferred name)
   - **Homepage URL**: `http://tms.uit.local:8084` (or your domain)
   - **Application description**: `Task Management System with GitHub integration`
   - **Authorization callback URL**: `http://tms.uit.local:8084/auth/github/callback`

### Step 2: Get OAuth Credentials

After creating the app:
1. Copy the **Client ID**
2. Generate and copy the **Client Secret**
3. Save these credentials securely

### Step 3: Configure OAuth Scopes

The application requests the following GitHub scopes:
- `user:email` - Access to user's email addresses

## 2. Environment Configuration

### Step 1: Copy Environment File

```bash
cp .env.example .env
```

### Step 2: Configure GitHub OAuth Variables

Edit the `.env` file and update the GitHub OAuth section:

```bash
# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here
GITHUB_REDIRECT_URI=http://tms.uit.local:8084/auth/github/callback
```

### Step 3: Configure Other Required Variables

Ensure these variables are also set:

```bash
# Docker Configuration
COMPOSE_PROJECT_NAME=tms-flask
NGINX_HOST=tms.uit.local
NGINX_HOST_HTTP_PORT=8084
FLASK_HOST_PORT=5001
MYSQL_HOST_PORT=3309

# Database
DB_NAME=tms
MYSQL_ROOT_PASSWORD=root

# JWT Secret (generate a secure key for production)
JWT_SECRET_KEY=your_secure_jwt_secret_key_here
```

## 3. Docker Setup

### Step 1: Add Host Entry (Optional)

Add this line to your `/etc/hosts` file for local development:

```
127.0.0.1 tms.uit.local
```

### Step 2: Start the Application

```bash
# Build and start all services
docker-compose up -d

# Check if services are running
docker-compose ps
```

### Step 3: Run Database Migrations

```bash
# Run migrations to set up the database schema
docker-compose exec flask_app python cmd/migrate.py
```

## 4. API Endpoints

The GitHub OAuth integration provides the following endpoints:

### 4.1 Get Authorization URL
```
GET /auth/github/url
```
Returns the GitHub authorization URL for testing purposes.

### 4.2 Initiate OAuth Flow
```
GET /auth/github
```
Redirects user to GitHub for authorization.

### 4.3 OAuth Callback
```
GET /auth/github/callback
```
Handles the GitHub OAuth callback and returns JWT token.

### 4.4 Link GitHub Account
```
POST /auth/github/link
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "access_token": "github_access_token"
}
```

### 4.5 Unlink GitHub Account
```
POST /auth/github/unlink
Authorization: Bearer <jwt_token>
```

## 5. Testing the OAuth Flow

### Method 1: Browser Testing

1. Navigate to `http://tms.uit.local:8084/auth/github`
2. You'll be redirected to GitHub for authorization
3. After authorization, you'll be redirected back with a JWT token

### Method 2: API Testing

1. Get the authorization URL:
   ```bash
   curl http://tms.uit.local:8084/auth/github/url
   ```

2. Open the returned URL in your browser
3. Complete the GitHub authorization
4. Extract the JWT token from the callback response

### Method 3: Swagger UI Testing

1. Navigate to `http://tms.uit.local:8084/docs` (if Swagger is configured)
2. Use the `/auth/github/url` endpoint to get the authorization URL
3. Follow the OAuth flow manually
