import sys, os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pytest
from flask_jwt_extended import create_access_token
from app.helpers.extensions import db
from app.models import User, Task, Project, Comment
from datetime import datetime

@pytest.fixture
def client():
    from app import create_app
    app = create_app({
        "TESTING": True,
        "SQLALCHEMY_DATABASE_URI": "sqlite:///:memory:",
        "JWT_SECRET_KEY": "test-secret"
    })

    with app.test_client() as client:
        with app.app_context():
            db.create_all()

            # Seed user first
            user = User(id=1, username="testuser", email="<EMAIL>", password_hash="hashed")
            db.session.add(user)
            db.session.commit()

            # Then seed project
            project = Project(id=1, name="Test Project", created_by=1, user_id=1)
            db.session.add(project)
            db.session.commit()

            # Then seed task with required fields
            task = Task(
                id=1,
                title="Test Task",
                created_by=1,
                assignee_id=1,
                project_id=1
            )
            db.session.add(task)
            db.session.commit()

        yield client

@pytest.fixture
def auth_headers(client):
    with client.application.app_context():
        token = create_access_token(identity="1")
    return {"Authorization": f"Bearer {token}"}

def test_add_comment(client, auth_headers):
    res = client.post("/comments/", json={
        "task_id": 1,
        "content": "This is a test comment."
    }, headers=auth_headers)

    assert res.status_code == 201
    assert res.json["message"] == "Comment added"
    assert "id" in res.json

def test_get_task_comments(client, auth_headers):
    res = client.post("/comments/", json={
        "task_id": 1,
        "content": "Another comment"
    }, headers=auth_headers)
    assert res.status_code == 201

    res = client.get("/comments/task/1", headers=auth_headers)
    assert res.status_code == 200
    assert isinstance(res.json, list)
    assert len(res.json) >= 1

def test_update_comment(client, auth_headers):
    res = client.post("/comments/", json={
        "task_id": 1,
        "content": "Old comment"
    }, headers=auth_headers)
    assert res.status_code == 201
    comment_id = res.json["id"]

    res = client.put(f"/comments/{comment_id}", json={
        "content": "Updated content"
    }, headers=auth_headers)
    assert res.status_code == 200
    assert res.json["message"] == "Comment updated"

def test_delete_comment(client, auth_headers):
    res = client.post("/comments/", json={
        "task_id": 1,
        "content": "To be deleted"
    }, headers=auth_headers)
    assert res.status_code == 201
    comment_id = res.json["id"]

    res = client.delete(f"/comments/{comment_id}", headers=auth_headers)
    assert res.status_code == 200
    assert res.json["message"] == "Comment deleted"
