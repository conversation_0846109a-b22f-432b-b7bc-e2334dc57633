
"""
Test cases for GitHub sync functionality
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))

import unittest
from unittest.mock import patch, MagicMock
from flask import Flask
from app.config import Config
from app.helpers.extensions import db
from app.models.user import User
from app.models.project import Project
from app.models.task import Task
from app.models.integration import Integration
from app.projects.github_service import GitHubRepositoryService
from app.tasks.github_service import GitHubIssuesService


class TestGitHubSync(unittest.TestCase):
    """Test GitHub sync functionality"""

    def setUp(self):
        """Set up test environment"""
        self.app = Flask(__name__)
        self.app.config.from_object(Config)
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        db.init_app(self.app)
        
        with self.app.app_context():
            db.create_all()
            
            # Create test user
            self.test_user = User(
                username="testuser",
                email="<EMAIL>",
                auth_provider="github",
                github_id="12345",
                github_username="testuser"
            )
            db.session.add(self.test_user)
            db.session.commit()
            
            # Create GitHub integration
            self.github_integration = Integration(
                user_id=self.test_user.id,
                platform="github",
                platform_user_id="12345",
                access_token="test_token",
                is_active=True
            )
            db.session.add(self.github_integration)
            db.session.commit()

    def tearDown(self):
        """Clean up test environment"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()

    @patch('app.projects.github_service.requests.get')
    def test_fetch_repositories(self, mock_get):
        """Test fetching repositories from GitHub"""
        # Mock GitHub API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {
                "id": 123456,
                "name": "test-repo",
                "full_name": "testuser/test-repo",
                "description": "Test repository",
                "html_url": "https://github.com/testuser/test-repo",
                "clone_url": "https://github.com/testuser/test-repo.git",
                "ssh_url": "**************:testuser/test-repo.git",
                "private": False,
                "fork": False,
                "language": "Python",
                "stargazers_count": 10,
                "forks_count": 5,
                "open_issues_count": 3,
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-12-01T00:00:00Z",
                "pushed_at": "2023-12-01T00:00:00Z",
                "default_branch": "main",
                "owner": {
                    "login": "testuser",
                    "id": 12345,
                    "avatar_url": "https://github.com/testuser.png"
                }
            }
        ]
        mock_get.return_value = mock_response
        
        with self.app.app_context():
            github_service = GitHubRepositoryService(self.test_user.id)
            response = github_service.get_all_user_repositories()
            
            self.assertTrue(response.success)
            self.assertEqual(len(response.data["repositories"]), 1)
            self.assertEqual(response.data["repositories"][0]["name"], "test-repo")

    def test_sync_repositories_to_projects(self):
        """Test syncing repositories to projects"""
        with self.app.app_context():
            github_service = GitHubRepositoryService(self.test_user.id)
            
            # Mock the get_repository_info method
            with patch.object(github_service, 'get_repository_info') as mock_get_repo:
                mock_get_repo.return_value = MagicMock(
                    success=True,
                    data={
                        "id": "123456",
                        "name": "test-repo",
                        "full_name": "testuser/test-repo",
                        "description": "Test repository",
                        "html_url": "https://github.com/testuser/test-repo"
                    }
                )
                
                repository_urls = ["https://github.com/testuser/test-repo"]
                response = github_service.sync_repositories_to_projects(repository_urls)
                
                self.assertTrue(response.success)
                self.assertEqual(response.data["total_synced"], 1)
                self.assertEqual(response.data["synced_projects"][0]["action"], "created")
                
                # Check if project was created in database
                project = Project.query.filter_by(github_repo_id="123456").first()
                self.assertIsNotNone(project)
                self.assertEqual(project.name, "test-repo")
                self.assertTrue(project.is_github_synced)

    @patch('app.tasks.github_service.requests.get')
    def test_sync_issues_to_tasks(self, mock_get):
        """Test syncing GitHub issues to tasks"""
        # Create a test project first
        with self.app.app_context():
            test_project = Project(
                name="test-repo",
                description="Test repository",
                created_by=self.test_user.id,
                user_id=self.test_user.id,
                github_repo_id="123456",
                github_repo_name="test-repo",
                github_repo_url="https://github.com/testuser/test-repo",
                github_repo_full_name="testuser/test-repo",
                is_github_synced=True
            )
            db.session.add(test_project)
            db.session.commit()
            
            # Mock GitHub API response for issues
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = [
                {
                    "id": 789012,
                    "number": 1,
                    "title": "Test Issue",
                    "body": "This is a test issue",
                    "state": "open",
                    "html_url": "https://github.com/testuser/test-repo/issues/1",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-12-01T00:00:00Z",
                    "closed_at": None,
                    "labels": [],
                    "assignees": [],
                    "user": {
                        "login": "testuser",
                        "id": 12345,
                        "avatar_url": "https://github.com/testuser.png"
                    },
                    "milestone": None,
                    "pull_request": None  # Not a pull request
                }
            ]
            mock_get.return_value = mock_response
            
            github_service = GitHubIssuesService(self.test_user.id)
            response = github_service.sync_repository_issues_to_tasks(test_project.id)
            
            self.assertTrue(response.success)
            self.assertEqual(response.data["total_synced"], 1)
            self.assertEqual(response.data["synced_tasks"][0]["action"], "created")
            
            # Check if task was created in database
            task = Task.query.filter_by(github_issue_id="789012").first()
            self.assertIsNotNone(task)
            self.assertEqual(task.title, "Test Issue")
            self.assertEqual(task.status, "To Do")  # open -> To Do
            self.assertTrue(task.is_github_synced)

    def test_github_service_without_integration(self):
        """Test GitHub service without integration should raise error"""
        with self.app.app_context():
            # Create user without GitHub integration
            user_without_github = User(
                username="nointegration",
                email="<EMAIL>",
                auth_provider="local"
            )
            db.session.add(user_without_github)
            db.session.commit()
            
            with self.assertRaises(ValueError) as context:
                GitHubRepositoryService(user_without_github.id)
            
            self.assertIn("No active GitHub integration found", str(context.exception))


if __name__ == '__main__':
    unittest.main()
